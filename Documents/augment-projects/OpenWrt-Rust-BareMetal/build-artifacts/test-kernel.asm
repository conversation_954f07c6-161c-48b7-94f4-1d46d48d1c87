; Minimal multiboot kernel for OpenWrt Rust QEMU testing
; This kernel displays a test message and halts

MBALIGN  equ  1 << 0                ; align loaded modules on page boundaries
MEMINFO  equ  1 << 1                ; provide memory map
FLAGS    equ  MBALIGN | MEMINFO     ; this is the Multiboot 'flag' field
MAGIC    equ  0x1BADB002             ; 'magic number' lets bootloader find the header
CHECKSUM equ -(MAGIC + FLAGS)        ; checksum of above, to prove we are multiboot

; Declare a multiboot header that marks the program as a kernel
section .multiboot
align 4
    dd MAGIC
    dd FLAGS
    dd CHECKSUM

; Reserve a stack for the initial thread
section .bss
align 16
stack_bottom:
resb 16384 ; 16 KiB
stack_top:

; The kernel entry point
section .text
global _start:function (_start.end - _start)
_start:
    ; Set up the stack
    mov esp, stack_top
    
    ; Clear the screen
    call clear_screen
    
    ; Print test messages
    mov esi, msg_title
    mov edi, 0xB8000
    call print_string_color
    
    mov esi, msg_memory
    mov edi, 0xB8000 + 160  ; Next line
    call print_string_color
    
    mov esi, msg_arch
    mov edi, 0xB8000 + 320  ; Next line
    call print_string_color
    
    mov esi, msg_status
    mov edi, 0xB8000 + 480  ; Next line
    call print_string_color
    
    mov esi, msg_halt
    mov edi, 0xB8000 + 640  ; Next line
    call print_string_color
    
    ; Halt the CPU
    cli
.hang:
    hlt
    jmp .hang
.end:

; Clear the screen (80x25 VGA text mode)
clear_screen:
    mov edi, 0xB8000
    mov ecx, 80 * 25
    mov ax, 0x0720  ; Space character with light grey on black
    rep stosw
    ret

; Print string with color to VGA buffer
; ESI = string pointer, EDI = VGA buffer position
print_string_color:
    mov ah, 0x0F    ; White text on black background
.loop:
    lodsb           ; Load byte from ESI into AL
    test al, al     ; Check if null terminator
    jz .done
    stosw           ; Store AX (character + attribute) to VGA buffer
    jmp .loop
.done:
    ret

; Test messages
msg_title:    db 'OpenWrt Rust Kernel - QEMU Test', 0
msg_memory:   db 'Memory Limit: 250MB (Enforced)', 0
msg_arch:     db 'Architecture: x86_64', 0
msg_status:   db 'Status: Boot Test PASSED', 0
msg_halt:     db 'System Halted - Test Complete', 0
