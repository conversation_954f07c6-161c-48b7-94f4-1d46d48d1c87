# Build Artifacts

This directory contains compiled binaries, kernel images, and build-related files for the OpenWrt-Rust-BareMetal project.

## Directory Structure

```
build-artifacts/
├── README.md                           # This file
├── kernels/                           # Compiled kernel binaries
├── binaries/                          # Other compiled binaries
├── linker.ld                          # Linker script
└── test-kernel.asm                    # Assembly output for debugging
```

## Kernel Binaries

The `kernels/` directory contains various kernel builds:

- **bootable-kernel.bin**: Standard bootable kernel
- **interactive-bootable.bin**: Interactive kernel with user input support
- **interactive-kernel.bin**: Interactive kernel variant
- **rust-test-kernel.bin**: Test kernel for Rust functionality
- **rust-test-kernel-flat.bin**: Flat binary version of test kernel
- **serial-kernel.bin**: Kernel with serial console support
- **test-kernel.bin**: Basic test kernel

## Build Files

- **linker.ld**: Linker script defining memory layout and sections
- **test-kernel.asm**: Assembly output for debugging and analysis

## Usage

These artifacts are generated by the build system and can be used for:

1. **QEMU Testing**: Boot kernels in QEMU for testing
2. **Hardware Deployment**: Flash kernels to actual hardware
3. **Debugging**: Analyze assembly output and memory layout
4. **Development**: Use as reference for new builds

## Regenerating Artifacts

To regenerate these artifacts, use the build scripts in the `scripts/` directory:

```bash
# Build all kernel variants
./scripts/create-bootable-kernel.py
./scripts/create-interactive-kernel.py
./scripts/create-serial-kernel.py

# Test kernels
./scripts/create-test-kernel.sh
```

## QEMU Testing

Use these kernels with QEMU for testing:

```bash
# Boot interactive kernel
./scripts/qemu-interactive.sh

# Run simple test
./scripts/qemu-simple-test.sh

# Monitor session
./scripts/qemu-monitor-interactive.sh
```

## Notes

- All binaries are compiled for x86_64 architecture by default
- Cross-compilation artifacts for other architectures are stored in `target/` directory
- These artifacts are gitignored and should be regenerated as needed
- For production deployment, use the build system in `openwrt-rust-modern/`
