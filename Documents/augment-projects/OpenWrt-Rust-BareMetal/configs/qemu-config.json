{"qemu_configurations": {"default": {"description": "Default OpenWrt Rust bare-metal configuration with 250MB memory limit", "architecture": "x86_64", "memory": "250M", "cpu": "qemu64", "machine": "q35", "smp": 1, "features": {"kvm": false, "graphics": false, "reboot": false, "shutdown": false}, "network": {"enabled": true, "type": "user", "device": "virtio-net-pci", "restrict": true}, "serial": {"enabled": true, "device": "stdio"}, "monitor": {"enabled": false}, "kernel_args": ["console=ttyS0", "panic=1", "oops=panic"]}, "minimal": {"description": "Minimal configuration for basic testing", "architecture": "x86_64", "memory": "128M", "cpu": "qemu64", "machine": "pc", "smp": 1, "features": {"kvm": false, "graphics": false, "reboot": false, "shutdown": false}, "network": {"enabled": false}, "serial": {"enabled": true, "device": "stdio"}, "monitor": {"enabled": false}, "kernel_args": ["console=ttyS0"]}, "debug": {"description": "Debug configuration with GDB support", "architecture": "x86_64", "memory": "250M", "cpu": "qemu64", "machine": "q35", "smp": 1, "features": {"kvm": false, "graphics": false, "reboot": false, "shutdown": false, "gdb": true, "gdb_port": 1234}, "network": {"enabled": true, "type": "user", "device": "virtio-net-pci", "restrict": true}, "serial": {"enabled": true, "device": "stdio"}, "monitor": {"enabled": true, "device": "unix:/tmp/qemu-monitor.sock,server,nowait"}, "kernel_args": ["console=ttyS0", "panic=1", "oops=panic", "debug"]}, "arm64": {"description": "ARM64 configuration for cross-architecture testing", "architecture": "aarch64", "memory": "250M", "cpu": "cortex-a57", "machine": "virt", "smp": 1, "features": {"kvm": false, "graphics": false, "reboot": false, "shutdown": false}, "network": {"enabled": true, "type": "user", "device": "virtio-net-pci", "restrict": true}, "serial": {"enabled": true, "device": "stdio"}, "monitor": {"enabled": false}, "kernel_args": ["console=ttyAMA0"]}, "stress_test": {"description": "Stress test configuration with maximum 250MB memory", "architecture": "x86_64", "memory": "250M", "cpu": "qemu64", "machine": "q35", "smp": 2, "features": {"kvm": false, "graphics": false, "reboot": false, "shutdown": false}, "network": {"enabled": true, "type": "user", "device": "virtio-net-pci", "restrict": false}, "serial": {"enabled": true, "device": "stdio"}, "monitor": {"enabled": true, "device": "unix:/tmp/qemu-monitor.sock,server,nowait"}, "kernel_args": ["console=ttyS0", "panic=1", "oops=panic", "stress_test=1"], "additional_devices": [{"type": "virtio-rng-pci"}, {"type": "virtio-balloon-pci"}]}}, "memory_constraints": {"default_limit": "250M", "minimum_limit": "64M", "maximum_limit": "250M", "description": "Memory constraints to prevent exceeding available memory during testing"}, "supported_architectures": [{"name": "x86_64", "qemu_binary": "qemu-system-x86_64", "default_machine": "q35", "default_cpu": "qemu64"}, {"name": "aarch64", "qemu_binary": "qemu-system-aarch64", "default_machine": "virt", "default_cpu": "cortex-a57"}, {"name": "arm", "qemu_binary": "qemu-system-arm", "default_machine": "virt", "default_cpu": "cortex-a15"}, {"name": "mips", "qemu_binary": "qemu-system-mips", "default_machine": "malta", "default_cpu": "24Kf"}, {"name": "mipsel", "qemu_binary": "qemu-system-mipsel", "default_machine": "malta", "default_cpu": "24Kf"}], "test_scenarios": [{"name": "boot_test", "description": "Basic boot test to verify kernel loads", "timeout": 30, "expected_output": ["OpenWrt", "Rust", "<PERSON><PERSON>"]}, {"name": "memory_test", "description": "Memory allocation and management test", "timeout": 60, "expected_output": ["Memory", "allocated", "freed"]}, {"name": "network_test", "description": "Network interface initialization test", "timeout": 45, "expected_output": ["Network", "interface", "up"]}, {"name": "stress_test", "description": "System stress test under memory constraints", "timeout": 120, "expected_output": ["Stress", "test", "completed"]}]}