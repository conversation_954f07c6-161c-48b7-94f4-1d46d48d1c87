{"firmware_integration": {"project": "OpenWrt Rust Bare-Metal", "packages": {"networking": {"ip-full": {"purpose": "Advanced IP routing for VLANs/VPNs", "integration": "Network stack configuration", "test_command": "ip addr show"}, "firewall": {"purpose": "Packet filtering and security", "integration": "Security layer integration", "test_command": "fw3 --help"}, "dnsmasq-full": {"purpose": "DHCP/DNS server with DoT/DoH", "integration": "Network services layer", "test_command": "dnsmasq --version"}, "iperf3": {"purpose": "Network bandwidth testing", "integration": "Performance testing framework", "test_command": "iperf3 --version"}, "tcpdump": {"purpose": "Network packet analysis", "integration": "Network debugging tools", "test_command": "tcpdump --version"}}, "security": {"openssh-sftp-server": {"purpose": "Secure file transfers", "integration": "Security services layer", "test_command": "sftp-server --help"}, "ca-bundle": {"purpose": "SSL certificate validation", "integration": "TLS/SSL infrastructure", "test_command": "ls /etc/ssl/certs/"}, "stunnel": {"purpose": "TLS/SSL tunneling", "integration": "Secure communication layer", "test_command": "stunnel -version"}}, "monitoring": {"vnstat": {"purpose": "Network traffic monitoring", "integration": "System monitoring framework", "test_command": "vnstat --version"}, "htop": {"purpose": "Process monitoring", "integration": "System resource monitoring", "test_command": "htop --version"}, "logread": {"purpose": "System log viewing", "integration": "Logging and debugging", "test_command": "logread -h"}, "sysstat": {"purpose": "System performance monitoring", "integration": "Performance analysis tools", "test_command": "iostat -V"}}, "admin": {"screen": {"purpose": "Terminal multiplexer", "integration": "Administrative tools", "test_command": "screen -version"}, "nano": {"purpose": "Text editor", "integration": "Configuration editing", "test_command": "nano --version"}, "curl": {"purpose": "HTTP/HTTPS testing", "integration": "Network testing tools", "test_command": "curl --version"}, "jq": {"purpose": "JSON processing", "integration": "Configuration and API tools", "test_command": "jq --version"}}}}}