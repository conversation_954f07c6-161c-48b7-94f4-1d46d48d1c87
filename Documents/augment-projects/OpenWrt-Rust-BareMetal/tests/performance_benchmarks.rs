//! Performance benchmarks for OpenWrt Rust firmware
//! Comprehensive testing suite for performance validation

use std::process::Command;
use std::time::{Duration, Instant};
use std::sync::Arc;
use std::sync::atomic::{AtomicU64, Ordering};

/// Performance metrics collection
#[derive(Debug, <PERSON>lone)]
pub struct PerformanceMetrics {
    pub memory_allocation_time_ns: u64,
    pub network_packet_processing_time_ns: u64,
    pub interrupt_latency_ns: u64,
    pub binary_size_bytes: u64,
    pub boot_time_ms: u64,
}

impl Default for PerformanceMetrics {
    fn default() -> Self {
        Self {
            memory_allocation_time_ns: 0,
            network_packet_processing_time_ns: 0,
            interrupt_latency_ns: 0,
            binary_size_bytes: 0,
            boot_time_ms: 0,
        }
    }
}

/// Benchmark memory allocation performance
#[test]
fn benchmark_memory_allocation() {
    let iterations = 10000;
    let mut total_time = Duration::new(0, 0);
    
    for _ in 0..iterations {
        let start = Instant::now();
        
        // Simulate memory allocation patterns
        let _vec: Vec<u8> = Vec::with_capacity(1024);
        
        total_time += start.elapsed();
    }
    
    let avg_time_ns = total_time.as_nanos() / iterations as u128;
    println!("Average memory allocation time: {} ns", avg_time_ns);
    
    // Performance threshold: should be under 1000ns per allocation
    assert!(avg_time_ns < 1000, "Memory allocation too slow: {} ns", avg_time_ns);
}

/// Benchmark network packet processing
#[test]
fn benchmark_network_processing() {
    let packet_sizes = vec![64, 256, 1024, 1500]; // Common packet sizes
    
    for size in packet_sizes {
        let iterations = 1000;
        let mut total_time = Duration::new(0, 0);
        
        for _ in 0..iterations {
            let start = Instant::now();
            
            // Simulate packet processing
            let packet_data = vec![0u8; size];
            let _checksum = packet_data.iter().fold(0u32, |acc, &x| acc.wrapping_add(x as u32));
            
            total_time += start.elapsed();
        }
        
        let avg_time_ns = total_time.as_nanos() / iterations as u128;
        println!("Average packet processing time ({}B): {} ns", size, avg_time_ns);
        
        // Performance threshold: should process packets quickly
        let max_time_ns = match size {
            64 => 500,    // Small packets: 500ns
            256 => 1000,  // Medium packets: 1μs
            1024 => 2000, // Large packets: 2μs
            1500 => 3000, // Jumbo packets: 3μs
            _ => 5000,
        };
        
        assert!(avg_time_ns < max_time_ns, 
                "Packet processing too slow for {}B packets: {} ns", size, avg_time_ns);
    }
}

/// Test binary size constraints
#[test]
fn test_binary_size_constraints() {
    let output = Command::new("cargo")
        .args(&["build", "--release", "--target", "x86_64-unknown-none"])
        .output()
        .expect("Failed to build project");
    
    assert!(output.status.success(), "Build failed: {}", 
            String::from_utf8_lossy(&output.stderr));
    
    // Check binary sizes
    let target_dir = std::path::Path::new("target/x86_64-unknown-none/release");
    
    if target_dir.exists() {
        let mut total_size = 0u64;
        
        for entry in std::fs::read_dir(target_dir).unwrap() {
            let entry = entry.unwrap();
            let metadata = entry.metadata().unwrap();
            
            if metadata.is_file() && is_executable(&entry.path()) {
                total_size += metadata.len();
            }
        }
        
        println!("Total binary size: {} bytes ({} KB)", total_size, total_size / 1024);
        
        // Size constraint: firmware should be under 2MB for embedded deployment
        assert!(total_size < 2 * 1024 * 1024, 
                "Binary too large: {} bytes (max: 2MB)", total_size);
    }
}

/// Check if a file is executable
fn is_executable(path: &std::path::Path) -> bool {
    #[cfg(unix)]
    {
        use std::os::unix::fs::PermissionsExt;
        if let Ok(metadata) = std::fs::metadata(path) {
            return metadata.permissions().mode() & 0o111 != 0;
        }
    }
    
    // Fallback: check file extension
    if let Some(extension) = path.extension() {
        matches!(extension.to_str(), Some("exe") | Some("bin"))
    } else {
        // No extension might indicate a Unix executable
        true
    }
}

/// Benchmark interrupt handling latency
#[test]
fn benchmark_interrupt_latency() {
    // This would require hardware-specific testing
    // For now, we simulate the measurement
    
    let simulated_latency_ns = 1500; // Typical embedded system latency
    
    println!("Simulated interrupt latency: {} ns", simulated_latency_ns);
    
    // Performance threshold: interrupt latency should be under 5μs
    assert!(simulated_latency_ns < 5000, 
            "Interrupt latency too high: {} ns", simulated_latency_ns);
}

/// Comprehensive performance test suite
#[test]
fn comprehensive_performance_test() {
    let mut metrics = PerformanceMetrics::default();
    
    // Memory allocation benchmark
    let start = Instant::now();
    let _vec: Vec<u8> = Vec::with_capacity(4096);
    metrics.memory_allocation_time_ns = start.elapsed().as_nanos() as u64;
    
    // Network processing benchmark
    let start = Instant::now();
    let packet = vec![0u8; 1500];
    let _processed = packet.iter().sum::<u8>();
    metrics.network_packet_processing_time_ns = start.elapsed().as_nanos() as u64;
    
    // Simulated interrupt latency
    metrics.interrupt_latency_ns = 1200;
    
    // Binary size check
    if let Ok(metadata) = std::fs::metadata("Cargo.toml") {
        metrics.binary_size_bytes = metadata.len(); // Placeholder
    }
    
    println!("Performance Metrics Summary:");
    println!("  Memory allocation: {} ns", metrics.memory_allocation_time_ns);
    println!("  Network processing: {} ns", metrics.network_packet_processing_time_ns);
    println!("  Interrupt latency: {} ns", metrics.interrupt_latency_ns);
    println!("  Binary size: {} bytes", metrics.binary_size_bytes);
    
    // Validate all metrics are within acceptable ranges
    assert!(metrics.memory_allocation_time_ns < 2000, "Memory allocation too slow");
    assert!(metrics.network_packet_processing_time_ns < 5000, "Network processing too slow");
    assert!(metrics.interrupt_latency_ns < 5000, "Interrupt latency too high");
}

/// Load testing for sustained performance
#[test]
fn load_test_sustained_performance() {
    let duration = Duration::from_secs(10);
    let start_time = Instant::now();
    let operations_counter = Arc::new(AtomicU64::new(0));
    
    while start_time.elapsed() < duration {
        // Simulate sustained workload
        let _vec: Vec<u8> = Vec::with_capacity(1024);
        let packet = vec![0u8; 512];
        let _checksum = packet.iter().fold(0u32, |acc, &x| acc.wrapping_add(x as u32));
        
        operations_counter.fetch_add(1, Ordering::Relaxed);
    }
    
    let total_operations = operations_counter.load(Ordering::Relaxed);
    let ops_per_second = total_operations / duration.as_secs();
    
    println!("Sustained performance: {} operations/second", ops_per_second);
    
    // Performance threshold: should maintain at least 10k ops/sec
    assert!(ops_per_second > 10_000, 
            "Sustained performance too low: {} ops/sec", ops_per_second);
}

/// Memory usage stability test
#[test]
fn test_memory_stability() {
    let iterations = 1000;
    let mut peak_memory = 0usize;
    
    for i in 0..iterations {
        // Allocate and deallocate memory
        let size = (i % 10 + 1) * 1024; // Variable sizes 1KB-10KB
        let vec: Vec<u8> = vec![0; size];
        
        // Track peak memory usage (simplified)
        peak_memory = peak_memory.max(vec.len());
        
        // Ensure memory is properly released
        drop(vec);
    }
    
    println!("Peak memory usage during test: {} bytes", peak_memory);
    
    // Memory should not grow excessively
    assert!(peak_memory < 100 * 1024, "Memory usage too high: {} bytes", peak_memory);
}
