# Cross-compilation configuration for OpenWrt targets

[build]
# Default target for development
target = "x86_64-unknown-linux-gnu"

# Target-specific configurations
[target.x86_64-unknown-linux-gnu]
rustflags = [
    "-C", "link-arg=-nostartfiles",
    "-C", "link-arg=-static",
    "-C", "relocation-model=static"
]

[target.armv7-unknown-linux-gnueabihf]
linker = "arm-linux-gnueabihf-gcc"
rustflags = [
    "-C", "link-arg=-nostartfiles",
    "-C", "link-arg=-static",
    "-C", "target-cpu=cortex-a9"
]

[target.aarch64-unknown-linux-gnu]
linker = "aarch64-linux-gnu-gcc"
rustflags = [
    "-C", "link-arg=-nostartfiles",
    "-C", "link-arg=-static",
    "-C", "target-cpu=cortex-a53"
]

[target.mips-unknown-linux-gnu]
linker = "mips-linux-gnu-gcc"
rustflags = [
    "-C", "link-arg=-nostartfiles",
    "-C", "link-arg=-static",
    "-C", "target-cpu=mips32r2"
]

[target.mipsel-unknown-linux-gnu]
linker = "mipsel-linux-gnu-gcc"
rustflags = [
    "-C", "link-arg=-nostartfiles",
    "-C", "link-arg=-static",
    "-C", "target-cpu=mips32r2"
]

# Embedded targets for bare-metal
[target.x86_64-unknown-none]
rustflags = [
    "-C", "link-arg=-Tlinker.ld",
    "-C", "link-arg=--nmagic",
    "-C", "link-arg=--gc-sections",
    "-C", "link-arg=--strip-all",
    "-C", "link-arg=--discard-all",
    "-C", "link-arg=-z,norelro",
    "-C", "target-feature=-mmx,-sse,+soft-float",
    "-C", "opt-level=z",
    "-C", "lto=fat",
]

[target.thumbv7em-none-eabihf]
runner = "probe-run --chip STM32F407VGTx"
rustflags = [
    "-C", "link-arg=-Tlink.x",
    "-C", "link-arg=-Tdefmt.x",
]

[target.thumbv7m-none-eabi]
runner = "probe-run"
rustflags = [
    "-C", "link-arg=-Tlink.x",
]

# Build optimization
[profile.dev]
incremental = true
debug = 1  # Reduced debug info to save memory during compilation
opt-level = 0

[profile.release]
incremental = false
debug = false
opt-level = "z"  # Optimize aggressively for size
lto = "fat"      # Use fat LTO for maximum size reduction
codegen-units = 1
panic = "abort"
strip = "symbols"
overflow-checks = false

# Reduce memory usage during compilation
[env]
CARGO_BUILD_JOBS = "2"  # Limit parallel jobs to reduce memory usage
