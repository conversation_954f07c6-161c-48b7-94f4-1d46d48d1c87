#!/bin/bash
# Enhanced build optimization script for OpenWrt Rust firmware
# Provides comprehensive build optimization for embedded deployment

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
TARGET_DIR="$PROJECT_ROOT/target"
OPTIMIZATION_LEVEL="${OPTIMIZATION_LEVEL:-max}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Clean previous builds
clean_build() {
    log_info "Cleaning previous builds..."
    cd "$PROJECT_ROOT"
    cargo clean
    rm -rf "$TARGET_DIR"
    log_success "Build cleaned"
}

# Optimize dependencies
optimize_dependencies() {
    log_info "Optimizing dependencies..."
    
    # Update Cargo.lock for optimal dependency resolution
    cargo update
    
    # Remove unused dependencies
    if command -v cargo-machete &> /dev/null; then
        cargo machete --fix
    else
        log_warning "cargo-machete not installed, skipping unused dependency removal"
    fi
    
    log_success "Dependencies optimized"
}

# Build with maximum optimization
build_optimized() {
    local target="$1"
    local profile="$2"
    
    log_info "Building optimized binary for target: $target, profile: $profile"
    
    cd "$PROJECT_ROOT"
    
    # Set optimization environment variables
    export RUSTFLAGS="-C target-cpu=native -C opt-level=z -C lto=fat -C codegen-units=1 -C panic=abort"
    export CARGO_PROFILE_RELEASE_LTO="fat"
    export CARGO_PROFILE_RELEASE_CODEGEN_UNITS="1"
    export CARGO_PROFILE_RELEASE_OPT_LEVEL="z"
    
    # Build with optimizations
    cargo build \
        --target "$target" \
        --profile "$profile" \
        --features "bare-metal" \
        --no-default-features \
        -Z build-std=core,alloc \
        -Z build-std-features=panic_immediate_abort
    
    log_success "Optimized build completed"
}

# Post-build optimizations
post_build_optimize() {
    local target="$1"
    local profile="$2"
    local binary_path="$TARGET_DIR/$target/$profile"
    
    log_info "Applying post-build optimizations..."
    
    # Strip symbols if not already done
    if command -v strip &> /dev/null; then
        find "$binary_path" -type f -executable -exec strip --strip-all {} \; 2>/dev/null || true
    fi
    
    # Compress binaries if upx is available
    if command -v upx &> /dev/null; then
        log_info "Compressing binaries with UPX..."
        find "$binary_path" -type f -executable -exec upx --best --lzma {} \; 2>/dev/null || true
    else
        log_warning "UPX not available, skipping binary compression"
    fi
    
    log_success "Post-build optimizations completed"
}

# Analyze binary size
analyze_binary_size() {
    local target="$1"
    local profile="$2"
    local binary_path="$TARGET_DIR/$target/$profile"
    
    log_info "Analyzing binary sizes..."
    
    if [ -d "$binary_path" ]; then
        echo "Binary sizes for $target ($profile):"
        find "$binary_path" -type f -executable -exec ls -lh {} \; | awk '{print $5 "\t" $9}'
        
        # Calculate total size
        local total_size=$(find "$binary_path" -type f -executable -exec stat -f%z {} \; 2>/dev/null | awk '{sum+=$1} END {print sum}' || \
                          find "$binary_path" -type f -executable -exec stat -c%s {} \; 2>/dev/null | awk '{sum+=$1} END {print sum}')
        
        if [ -n "$total_size" ]; then
            log_info "Total binary size: $(numfmt --to=iec-i --suffix=B $total_size)"
        fi
    fi
}

# Generate size report
generate_size_report() {
    local target="$1"
    local profile="$2"
    local report_file="$PROJECT_ROOT/size-report-$target-$profile.txt"
    
    log_info "Generating size report..."
    
    {
        echo "OpenWrt Rust Firmware Size Report"
        echo "=================================="
        echo "Target: $target"
        echo "Profile: $profile"
        echo "Generated: $(date)"
        echo ""
        
        if command -v cargo-bloat &> /dev/null; then
            echo "Cargo Bloat Analysis:"
            echo "--------------------"
            cargo bloat --target "$target" --profile "$profile" --crates
            echo ""
        fi
        
        echo "Binary Sizes:"
        echo "-------------"
        analyze_binary_size "$target" "$profile"
        
    } > "$report_file"
    
    log_success "Size report generated: $report_file"
}

# Main optimization workflow
main() {
    local target="${1:-x86_64-unknown-none}"
    local profile="${2:-release}"
    
    log_info "Starting build optimization for $target ($profile)"
    
    # Optimization steps
    clean_build
    optimize_dependencies
    build_optimized "$target" "$profile"
    post_build_optimize "$target" "$profile"
    analyze_binary_size "$target" "$profile"
    generate_size_report "$target" "$profile"
    
    log_success "Build optimization completed successfully!"
}

# Handle command line arguments
case "${1:-}" in
    clean)
        clean_build
        ;;
    deps)
        optimize_dependencies
        ;;
    build)
        build_optimized "${2:-x86_64-unknown-none}" "${3:-release}"
        ;;
    analyze)
        analyze_binary_size "${2:-x86_64-unknown-none}" "${3:-release}"
        ;;
    report)
        generate_size_report "${2:-x86_64-unknown-none}" "${3:-release}"
        ;;
    *)
        main "$@"
        ;;
esac
