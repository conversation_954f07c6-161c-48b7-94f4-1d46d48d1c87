#!/bin/bash

# Interactive QEMU session for OpenWrt Rust bare-metal firmware
# Allows full interaction with the running firmware

set -euo pipefail

# Configuration
MEMORY_LIMIT="250M"
ARCHITECTURE="x86_64"
QEMU_BINARY="qemu-system-x86_64"
KERNEL_PATH="serial-kernel.bin"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_interactive() {
    echo -e "${CYAN}[INTERACTIVE]${NC} $1"
}

# Check if QEMU is available
check_qemu() {
    if ! command -v "$QEMU_BINARY" >/dev/null 2>&1; then
        log_error "QEMU not found: $QEMU_BINARY"
        log_info "Please install QEMU:"
        log_info "  macOS: brew install qemu"
        log_info "  Ubuntu/Debian: sudo apt-get install qemu-system-x86"
        exit 1
    fi
    log_success "QEMU found: $QEMU_BINARY"
}

# Check if kernel exists
check_kernel() {
    if [[ ! -f "$KERNEL_PATH" ]]; then
        log_error "Kernel not found: $KERNEL_PATH"
        log_info "Building kernel..."
        
        # Try to build the kernel
        if cargo build --bin simple-qemu-test --target x86_64-unknown-none --release; then
            # Convert to flat binary
            if command -v cargo-objcopy >/dev/null 2>&1; then
                cargo objcopy --bin simple-qemu-test --target x86_64-unknown-none --release -- --output-target=binary rust-test-kernel-flat.bin
                log_success "Kernel built and converted: $KERNEL_PATH"
            else
                log_error "cargo-objcopy not found. Please install: cargo install cargo-binutils"
                exit 1
            fi
        else
            log_error "Failed to build kernel"
            exit 1
        fi
    fi
    log_success "Kernel found: $KERNEL_PATH ($(stat -f%z "$KERNEL_PATH" 2>/dev/null || stat -c%s "$KERNEL_PATH" 2>/dev/null) bytes)"
}

# Show interactive instructions
show_instructions() {
    echo ""
    echo "=========================================="
    echo "OpenWrt Rust Firmware - Interactive QEMU"
    echo "=========================================="
    echo ""
    log_interactive "Starting interactive QEMU session..."
    echo ""
    echo "📋 INTERACTION GUIDE:"
    echo "  • Your Rust firmware will boot and display messages"
    echo "  • Serial console is connected to this terminal"
    echo "  • Press Ctrl+A, then C to access QEMU monitor"
    echo "  • Press Ctrl+A, then X to exit QEMU"
    echo "  • Type 'info registers' in monitor to see CPU state"
    echo "  • Type 'info memory' in monitor to see memory usage"
    echo "  • Session will be logged for later review"
    echo ""
    echo "🔧 QEMU CONFIGURATION:"
    echo "  • Memory: $MEMORY_LIMIT (enforced limit)"
    echo "  • Architecture: $ARCHITECTURE"
    echo "  • Machine: Q35 (modern PC)"
    echo "  • Network: User mode (restricted)"
    echo "  • Console: Serial (this terminal)"
    echo ""
    echo "🚀 FIRMWARE FEATURES:"
    echo "  • Rust bare-metal kernel"
    echo "  • Serial output for debugging"
    echo "  • Memory-safe implementation"
    echo "  • OpenWrt integration ready"
    echo ""
    echo "Press Enter to start QEMU, or Ctrl+C to cancel..."
    read -r
}

# Run interactive QEMU session
run_interactive_qemu() {
    log_info "Starting interactive QEMU session..."
    echo ""
    log_interactive "QEMU Command:"
    echo ""
    
    # Show the command being executed
    cat << EOF
$QEMU_BINARY \\
  -kernel "$KERNEL_PATH" \\
  -m $MEMORY_LIMIT \\
  -smp 1 \\
  -machine q35 \\
  -cpu qemu64 \\
  -nographic \\
  -serial mon:stdio \\
  -netdev user,id=net0,restrict=on \\
  -device virtio-net-pci,netdev=net0 \\
  -object memory-backend-ram,id=ram,size=$MEMORY_LIMIT \\
  -numa node,memdev=ram \\
  -append "console=ttyS0 panic=1 oops=panic"
EOF
    
    echo ""
    echo "=========================================="
    log_interactive "FIRMWARE BOOTING - INTERACTIVE MODE"
    echo "=========================================="
    echo ""
    
    # Create log directory and file
    mkdir -p qemu-logs
    local log_file="qemu-logs/qemu-interactive-$(date +%Y%m%d-%H%M%S).log"
    
    # Run QEMU in interactive mode (no timeout)
    echo "[INFO] Starting QEMU with interactive monitor..."
    echo "[INFO] You can use QEMU monitor commands to interact with the system"
    echo "[INFO] Press Ctrl+A, then C to access QEMU monitor"
    echo "[INFO] Press Ctrl+A, then X to exit QEMU"
    echo ""

    $QEMU_BINARY \
        -kernel "$KERNEL_PATH" \
        -m $MEMORY_LIMIT \
        -smp 1 \
        -machine q35 \
        -cpu qemu64 \
        -nographic \
        -serial mon:stdio \
        -netdev user,id=net0,restrict=on \
        -device virtio-net-pci,netdev=net0 \
        -object memory-backend-ram,id=ram,size=$MEMORY_LIMIT \
        -numa node,memdev=ram \
        -append "console=ttyS0 panic=1 oops=panic" \
        2>&1 | tee "$log_file"
    
    local exit_code=$?
    echo ""
    echo "=========================================="
    log_interactive "QEMU SESSION ENDED"
    echo "=========================================="
    
    if [[ $exit_code -eq 0 ]]; then
        log_success "QEMU session completed successfully"
    else
        log_warning "QEMU exited with code: $exit_code"
    fi
    
    log_info "Session log saved to: $log_file"
    
    # Show log summary
    if [[ -f "$log_file" ]]; then
        echo ""
        log_info "Last 10 lines of session log:"
        echo "----------------------------------------"
        tail -10 "$log_file"
        echo "----------------------------------------"
    fi
}

# Show QEMU monitor commands
show_monitor_help() {
    echo ""
    echo "🔧 QEMU MONITOR COMMANDS (Ctrl+A, C):"
    echo "  info registers    - Show CPU registers"
    echo "  info memory       - Show memory usage"
    echo "  info network      - Show network status"
    echo "  info cpus         - Show CPU information"
    echo "  system_reset      - Reset the system"
    echo "  quit              - Exit QEMU"
    echo "  help              - Show all commands"
    echo ""
}

# Main execution
main() {
    echo ""
    echo "🚀 OpenWrt Rust Firmware - Interactive QEMU Session"
    echo ""
    
    # Pre-flight checks
    check_qemu
    check_kernel
    
    # Show monitor help
    show_monitor_help
    
    # Show instructions and wait for user
    show_instructions
    
    # Run the interactive session
    run_interactive_qemu
    
    echo ""
    log_success "Interactive QEMU session completed!"
    echo ""
    log_info "To run again: ./qemu-interactive.sh"
    log_info "To run with timeout: ./qemu-simple-test.sh"
    echo ""
}

# Handle Ctrl+C gracefully
trap 'echo ""; log_warning "Interrupted by user"; exit 130' INT

# Run main function
main "$@"
