#!/bin/bash

# Create a proper multiboot test kernel for QEMU testing

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
KERNEL_PATH="$SCRIPT_DIR/test-kernel.bin"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Create multiboot assembly kernel
create_multiboot_kernel() {
    log_info "Creating multiboot test kernel..."
    
    # Create assembly source
    cat > "$SCRIPT_DIR/test-kernel.asm" << 'EOF'
; Minimal multiboot kernel for OpenWrt Rust QEMU testing
; This kernel displays a test message and halts

MBALIGN  equ  1 << 0                ; align loaded modules on page boundaries
MEMINFO  equ  1 << 1                ; provide memory map
FLAGS    equ  MBALIGN | MEMINFO     ; this is the Multiboot 'flag' field
MAGIC    equ  0x1BADB002             ; 'magic number' lets bootloader find the header
CHECKSUM equ -(MAGIC + FLAGS)        ; checksum of above, to prove we are multiboot

; Declare a multiboot header that marks the program as a kernel
section .multiboot
align 4
    dd MAGIC
    dd FLAGS
    dd CHECKSUM

; Reserve a stack for the initial thread
section .bss
align 16
stack_bottom:
resb 16384 ; 16 KiB
stack_top:

; The kernel entry point
section .text
global _start:function (_start.end - _start)
_start:
    ; Set up the stack
    mov esp, stack_top
    
    ; Clear the screen
    call clear_screen
    
    ; Print test messages
    mov esi, msg_title
    mov edi, 0xB8000
    call print_string_color
    
    mov esi, msg_memory
    mov edi, 0xB8000 + 160  ; Next line
    call print_string_color
    
    mov esi, msg_arch
    mov edi, 0xB8000 + 320  ; Next line
    call print_string_color
    
    mov esi, msg_status
    mov edi, 0xB8000 + 480  ; Next line
    call print_string_color
    
    mov esi, msg_halt
    mov edi, 0xB8000 + 640  ; Next line
    call print_string_color
    
    ; Halt the CPU
    cli
.hang:
    hlt
    jmp .hang
.end:

; Clear the screen (80x25 VGA text mode)
clear_screen:
    mov edi, 0xB8000
    mov ecx, 80 * 25
    mov ax, 0x0720  ; Space character with light grey on black
    rep stosw
    ret

; Print string with color to VGA buffer
; ESI = string pointer, EDI = VGA buffer position
print_string_color:
    mov ah, 0x0F    ; White text on black background
.loop:
    lodsb           ; Load byte from ESI into AL
    test al, al     ; Check if null terminator
    jz .done
    stosw           ; Store AX (character + attribute) to VGA buffer
    jmp .loop
.done:
    ret

; Test messages
msg_title:    db 'OpenWrt Rust Kernel - QEMU Test', 0
msg_memory:   db 'Memory Limit: 250MB (Enforced)', 0
msg_arch:     db 'Architecture: x86_64', 0
msg_status:   db 'Status: Boot Test PASSED', 0
msg_halt:     db 'System Halted - Test Complete', 0
EOF

    # Create linker script
    cat > "$SCRIPT_DIR/linker.ld" << 'EOF'
ENTRY(_start)

SECTIONS
{
    . = 1M;

    .text BLOCK(4K) : ALIGN(4K)
    {
        *(.multiboot)
        *(.text)
    }

    .rodata BLOCK(4K) : ALIGN(4K)
    {
        *(.rodata)
    }

    .data BLOCK(4K) : ALIGN(4K)
    {
        *(.data)
    }

    .bss BLOCK(4K) : ALIGN(4K)
    {
        *(COMMON)
        *(.bss)
    }
}
EOF

    # Try to assemble and link
    if command -v nasm >/dev/null 2>&1; then
        log_info "Assembling kernel with NASM..."
        nasm -felf32 "$SCRIPT_DIR/test-kernel.asm" -o "$SCRIPT_DIR/test-kernel.o"
        
        if command -v ld >/dev/null 2>&1; then
            log_info "Linking kernel..."
            ld -m elf_i386 -T "$SCRIPT_DIR/linker.ld" -o "$KERNEL_PATH" "$SCRIPT_DIR/test-kernel.o"
            
            # Verify the kernel
            if file "$KERNEL_PATH" | grep -q "ELF"; then
                log_success "Multiboot kernel created successfully: $KERNEL_PATH"
                return 0
            else
                log_warning "Kernel verification failed, creating fallback"
            fi
        else
            log_warning "Linker not found, creating fallback kernel"
        fi
    else
        log_warning "NASM not found, creating fallback kernel"
    fi
    
    # Fallback: create a simple binary that QEMU can load
    create_fallback_kernel
}

# Create a simple fallback kernel
create_fallback_kernel() {
    log_info "Creating fallback kernel..."

    # Create a raw binary kernel that QEMU can load directly
    python3 -c "
# Create a simple raw binary kernel for QEMU
# This will be loaded at 0x100000 (1MB) by QEMU

kernel_code = bytearray(4096)  # 4KB kernel

# x86 assembly instructions as bytes:
# mov ax, 0x07    ; White text on black background
# mov dx, 0x3D4   ; VGA CRTC Address Register
# mov al, 0x0A    ; Cursor Start Register
# out dx, al
# inc dx
# mov al, 0x20    ; Hide cursor
# out dx, al

# Print test message to VGA text buffer at 0xB8000
# mov esi, message_offset
# mov edi, 0xB8000
# mov ecx, message_length
# mov ah, 0x0F    ; White on black attribute

code_offset = 0
instructions = [
    # Set up VGA text mode and clear screen
    0xB8, 0x00, 0x03,           # mov ax, 0x0300 (80x25 text mode)
    0xCD, 0x10,                 # int 0x10 (BIOS video interrupt)

    # Write test message directly to VGA buffer
    0xBE, 0x00, 0x02, 0x00, 0x00,  # mov esi, 0x200 (message offset)
    0xBF, 0x00, 0x80, 0x0B, 0x00,  # mov edi, 0xB8000 (VGA buffer)
    0xB4, 0x0F,                     # mov ah, 0x0F (white on black)

    # Copy loop
    0xAC,                       # lodsb (load byte from [esi] to al)
    0x84, 0xC0,                 # test al, al (check for null terminator)
    0x74, 0x04,                 # jz end (jump if zero)
    0xAB,                       # stosw (store ax to [edi])
    0xEB, 0xF8,                 # jmp copy_loop

    # Halt the system
    0xFA,                       # cli (disable interrupts)
    0xF4,                       # hlt (halt)
    0xEB, 0xFD,                 # jmp halt_loop (infinite loop)
]

# Add instructions to kernel
for i, byte_val in enumerate(instructions):
    if i < len(kernel_code):
        kernel_code[i] = byte_val

# Add test message at offset 0x200
message = b'OpenWrt Rust Kernel - QEMU Test (250MB Memory Limit)\\0'
message_offset = 0x200
for i, char in enumerate(message):
    if message_offset + i < len(kernel_code):
        kernel_code[message_offset + i] = char

# Write the raw binary kernel
with open('$KERNEL_PATH', 'wb') as f:
    f.write(kernel_code)
"

    if [[ -f "$KERNEL_PATH" ]]; then
        log_success "Fallback kernel created: $KERNEL_PATH"
    else
        log_error "Failed to create kernel"
        exit 1
    fi
}

# Main execution
main() {
    echo "OpenWrt Rust Test Kernel Creator"
    echo "================================"
    
    if [[ -f "$KERNEL_PATH" ]]; then
        log_info "Test kernel already exists: $KERNEL_PATH"
        read -p "Recreate kernel? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "Using existing kernel"
            exit 0
        fi
        rm -f "$KERNEL_PATH"
    fi
    
    create_multiboot_kernel
    
    # Verify the created kernel
    if [[ -f "$KERNEL_PATH" ]]; then
        log_info "Kernel size: $(stat -f%z "$KERNEL_PATH" 2>/dev/null || stat -c%s "$KERNEL_PATH" 2>/dev/null || echo "unknown") bytes"
        log_info "Kernel type: $(file "$KERNEL_PATH" 2>/dev/null || echo "unknown")"
        log_success "Test kernel ready for QEMU testing"
    else
        log_error "Failed to create test kernel"
        exit 1
    fi
}

main "$@"
