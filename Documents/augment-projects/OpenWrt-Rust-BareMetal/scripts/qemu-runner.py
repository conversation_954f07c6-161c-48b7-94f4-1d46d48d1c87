#!/usr/bin/env python3
"""
OpenWrt Rust Bare-Metal QEMU Test Runner
Advanced QEMU testing with memory constraints and configuration management
"""

import json
import subprocess
import sys
import time
import argparse
import os
import signal
from pathlib import Path
from typing import Dict, List, Optional, Tuple

class QEMUTestRunner:
    def __init__(self, config_file: str = "qemu-config.json"):
        self.project_root = Path(__file__).parent
        self.config_file = self.project_root / config_file
        self.config = self._load_config()
        self.log_dir = self.project_root / "qemu-logs"
        self.log_dir.mkdir(exist_ok=True)
        
    def _load_config(self) -> Dict:
        """Load QEMU configuration from JSON file"""
        try:
            with open(self.config_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"Error: Configuration file {self.config_file} not found")
            sys.exit(1)
        except json.JSONDecodeError as e:
            print(f"Error: Invalid JSON in {self.config_file}: {e}")
            sys.exit(1)
    
    def _validate_memory_limit(self, memory: str) -> bool:
        """Validate memory limit against constraints"""
        constraints = self.config.get("memory_constraints", {})
        max_limit = constraints.get("maximum_limit", "250M")
        
        # Extract numeric value
        mem_value = int(''.join(filter(str.isdigit, memory)))
        max_value = int(''.join(filter(str.isdigit, max_limit)))
        
        if mem_value > max_value:
            print(f"Error: Memory limit {memory} exceeds maximum {max_limit}")
            return False
        return True
    
    def _build_qemu_command(self, config_name: str, kernel_path: str) -> List[str]:
        """Build QEMU command from configuration"""
        if config_name not in self.config["qemu_configurations"]:
            raise ValueError(f"Configuration '{config_name}' not found")
        
        cfg = self.config["qemu_configurations"][config_name]
        arch = cfg["architecture"]
        
        # Find QEMU binary
        qemu_binary = None
        for arch_info in self.config["supported_architectures"]:
            if arch_info["name"] == arch:
                qemu_binary = arch_info["qemu_binary"]
                break
        
        if not qemu_binary:
            raise ValueError(f"Unsupported architecture: {arch}")
        
        # Validate memory limit
        if not self._validate_memory_limit(cfg["memory"]):
            raise ValueError("Memory limit validation failed")
        
        # Build command
        cmd = [
            qemu_binary,
            "-kernel", kernel_path,
            "-m", cfg["memory"],
            "-smp", str(cfg["smp"]),
            "-machine", cfg["machine"],
            "-cpu", cfg["cpu"]
        ]
        
        # Add features
        features = cfg.get("features", {})
        if not features.get("graphics", True):
            cmd.append("-nographic")
        if not features.get("reboot", True):
            cmd.append("-no-reboot")
        if not features.get("shutdown", True):
            cmd.append("-no-shutdown")
        if features.get("gdb", False):
            gdb_port = features.get("gdb_port", 1234)
            cmd.extend(["-s", "-S"])
        
        # Add network
        network = cfg.get("network", {})
        if network.get("enabled", False):
            net_type = network.get("type", "user")
            restrict = ",restrict=on" if network.get("restrict", False) else ""
            cmd.extend(["-netdev", f"{net_type},id=net0{restrict}"])
            cmd.extend(["-device", f"{network.get('device', 'virtio-net-pci')},netdev=net0"])
        
        # Add serial
        serial = cfg.get("serial", {})
        if serial.get("enabled", False):
            cmd.extend(["-serial", serial.get("device", "stdio")])
        
        # Add monitor
        monitor = cfg.get("monitor", {})
        if monitor.get("enabled", False):
            cmd.extend(["-monitor", monitor.get("device", "stdio")])
        else:
            cmd.extend(["-monitor", "none"])
        
        # Add kernel arguments
        kernel_args = cfg.get("kernel_args", [])
        if kernel_args:
            cmd.extend(["-append", " ".join(kernel_args)])
        
        # Add additional devices
        additional_devices = cfg.get("additional_devices", [])
        for device in additional_devices:
            cmd.extend(["-device", device["type"]])
        
        return cmd
    
    def _create_test_kernel(self) -> str:
        """Create a minimal test kernel if none exists"""
        kernel_path = self.project_root / "test-kernel.bin"
        
        if not kernel_path.exists():
            print("Creating minimal test kernel...")
            # Create a minimal ELF header for testing
            with open(kernel_path, 'wb') as f:
                # ELF header
                f.write(b'\x7fELF\x01\x01\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00')
                f.write(b'\x02\x00\x03\x00\x01\x00\x00\x00\x00\x10\x00\x00')
                f.write(b'\x34\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00')
                f.write(b'\x34\x00\x20\x00\x01\x00\x00\x00\x00\x00\x00\x00')
                # Program header
                f.write(b'\x01\x00\x00\x00\x00\x00\x00\x00\x00\x10\x00\x00')
                f.write(b'\x00\x10\x00\x00\x00\x10\x00\x00\x00\x10\x00\x00')
                f.write(b'\x05\x00\x00\x00\x00\x10\x00\x00')
                # Minimal code that halts
                f.write(b'\xfa\xf4')  # cli; hlt
            
            print(f"Test kernel created: {kernel_path}")
        
        return str(kernel_path)
    
    def run_test(self, config_name: str = "default", kernel_path: Optional[str] = None, 
                 timeout: int = 30, scenario: Optional[str] = None) -> Tuple[bool, str]:
        """Run QEMU test with specified configuration"""
        
        # Determine kernel path
        if kernel_path is None:
            kernel_path = self._create_test_kernel()
        elif not os.path.exists(kernel_path):
            print(f"Error: Kernel file not found: {kernel_path}")
            return False, "Kernel file not found"
        
        # Build QEMU command
        try:
            cmd = self._build_qemu_command(config_name, kernel_path)
        except ValueError as e:
            print(f"Error building QEMU command: {e}")
            return False, str(e)
        
        # Create log file
        timestamp = time.strftime("%Y%m%d-%H%M%S")
        log_file = self.log_dir / f"qemu-{config_name}-{timestamp}.log"
        
        print(f"Starting QEMU test: {config_name}")
        print(f"Command: {' '.join(cmd)}")
        print(f"Timeout: {timeout}s")
        print(f"Log file: {log_file}")
        
        # Run QEMU
        try:
            with open(log_file, 'w') as log:
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    universal_newlines=True,
                    preexec_fn=os.setsid if os.name != 'nt' else None
                )
                
                output_lines = []
                start_time = time.time()
                
                while True:
                    # Check timeout
                    if time.time() - start_time > timeout:
                        print("Test timed out")
                        try:
                            os.killpg(os.getpgid(process.pid), signal.SIGTERM)
                        except:
                            process.terminate()
                        break

                    # Check if process ended first
                    if process.poll() is not None:
                        # Read any remaining output
                        remaining = process.stdout.read()
                        if remaining:
                            output_lines.append(remaining.strip())
                            log.write(remaining)
                            print(f"QEMU: {remaining.strip()}")
                        break

                    # Read output with timeout
                    import select
                    if select.select([process.stdout], [], [], 0.1)[0]:
                        line = process.stdout.readline()
                        if line:
                            output_lines.append(line.strip())
                            log.write(line)
                            log.flush()
                            print(f"QEMU: {line.strip()}")

                    time.sleep(0.1)
                
                # Wait for process to finish
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                
                return_code = process.returncode
                output = '\n'.join(output_lines)
                
                # Validate scenario if specified
                if scenario:
                    success = self._validate_scenario(scenario, output)
                else:
                    success = return_code == 0 or return_code == 124  # 124 is timeout
                
                print(f"Test completed with return code: {return_code}")
                return success, output
                
        except Exception as e:
            error_msg = f"Error running QEMU: {e}"
            print(error_msg)
            return False, error_msg
    
    def _validate_scenario(self, scenario_name: str, output: str) -> bool:
        """Validate test output against scenario expectations"""
        scenarios = self.config.get("test_scenarios", [])
        scenario = next((s for s in scenarios if s["name"] == scenario_name), None)
        
        if not scenario:
            print(f"Warning: Scenario '{scenario_name}' not found")
            return True
        
        expected_output = scenario.get("expected_output", [])
        for expected in expected_output:
            if expected.lower() not in output.lower():
                print(f"Expected output '{expected}' not found")
                return False
        
        return True
    
    def list_configurations(self):
        """List available QEMU configurations"""
        print("Available QEMU configurations:")
        for name, cfg in self.config["qemu_configurations"].items():
            print(f"  {name}: {cfg.get('description', 'No description')}")
    
    def list_scenarios(self):
        """List available test scenarios"""
        print("Available test scenarios:")
        for scenario in self.config.get("test_scenarios", []):
            print(f"  {scenario['name']}: {scenario.get('description', 'No description')}")

def main():
    parser = argparse.ArgumentParser(description="OpenWrt Rust QEMU Test Runner")
    parser.add_argument("-c", "--config", default="default", 
                       help="QEMU configuration to use")
    parser.add_argument("-k", "--kernel", 
                       help="Path to kernel binary")
    parser.add_argument("-t", "--timeout", type=int, default=30,
                       help="Test timeout in seconds")
    parser.add_argument("-s", "--scenario",
                       help="Test scenario to validate")
    parser.add_argument("--list-configs", action="store_true",
                       help="List available configurations")
    parser.add_argument("--list-scenarios", action="store_true",
                       help="List available test scenarios")
    
    args = parser.parse_args()
    
    runner = QEMUTestRunner()
    
    if args.list_configs:
        runner.list_configurations()
        return
    
    if args.list_scenarios:
        runner.list_scenarios()
        return
    
    success, output = runner.run_test(
        config_name=args.config,
        kernel_path=args.kernel,
        timeout=args.timeout,
        scenario=args.scenario
    )
    
    if success:
        print("Test PASSED")
        sys.exit(0)
    else:
        print("Test FAILED")
        sys.exit(1)

if __name__ == "__main__":
    main()
