#!/bin/bash

# OpenWrt Rust Bare-Metal QEMU Test Setup
# Memory-constrained testing environment with 250MB limit

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR"
MEMORY_LIMIT="250M"
QEMU_ARCH="x86_64"
QEMU_BINARY="qemu-system-x86_64"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if QEMU is installed
check_qemu() {
    if ! command -v "$QEMU_BINARY" >/dev/null 2>&1; then
        log_error "QEMU binary not found: $QEMU_BINARY"
        log_info "Please install QEMU:"
        log_info "  macOS: brew install qemu"
        log_info "  Ubuntu/Debian: sudo apt-get install qemu-system-x86"
        log_info "  CentOS/RHEL: sudo yum install qemu-kvm"
        exit 1
    fi
    log_success "QEMU binary found: $QEMU_BINARY"
}

# Create a minimal test kernel if none exists
create_test_kernel() {
    local kernel_path="$PROJECT_ROOT/test-kernel.bin"
    
    if [[ ! -f "$kernel_path" ]]; then
        log_info "Creating minimal test kernel..."
        
        # Create a minimal multiboot kernel for testing
        cat > "$PROJECT_ROOT/test-kernel.asm" << 'EOF'
; Minimal multiboot kernel for QEMU testing
MBALIGN  equ  1 << 0
MEMINFO  equ  1 << 1
FLAGS    equ  MBALIGN | MEMINFO
MAGIC    equ  0x1BADB002
CHECKSUM equ -(MAGIC + FLAGS)

section .multiboot
align 4
    dd MAGIC
    dd FLAGS
    dd CHECKSUM

section .bss
align 16
stack_bottom:
resb 16384 ; 16 KiB
stack_top:

section .text
global _start:function (_start.end - _start)
_start:
    mov esp, stack_top
    
    ; Print test message
    mov esi, test_msg
    call print_string
    
    ; Halt the system
    cli
.hang:  hlt
    jmp .hang
.end:

print_string:
    mov edi, 0xB8000  ; VGA text buffer
    mov ah, 0x07      ; Light grey on black
.loop:
    lodsb
    test al, al
    jz .done
    stosw
    jmp .loop
.done:
    ret

test_msg: db 'OpenWrt Rust Kernel Test - Memory: 250MB', 0
EOF

        # Try to assemble the kernel
        if command -v nasm >/dev/null 2>&1; then
            nasm -felf32 "$PROJECT_ROOT/test-kernel.asm" -o "$PROJECT_ROOT/test-kernel.o"
            if command -v ld >/dev/null 2>&1; then
                ld -m elf_i386 -T "$PROJECT_ROOT/linker.ld" -o "$kernel_path" "$PROJECT_ROOT/test-kernel.o" 2>/dev/null || {
                    log_warning "Failed to link kernel, creating dummy binary"
                    echo -e '\x7fELF' > "$kernel_path"
                }
            else
                log_warning "Linker not found, creating dummy binary"
                echo -e '\x7fELF' > "$kernel_path"
            fi
        else
            log_warning "NASM not found, creating dummy binary for testing"
            # Create a minimal ELF header for testing
            printf '\x7fELF\x01\x01\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00' > "$kernel_path"
        fi
        
        log_success "Test kernel created: $kernel_path"
    else
        log_info "Test kernel already exists: $kernel_path"
    fi
    
    echo "$kernel_path"
}

# Create linker script if needed
create_linker_script() {
    local linker_script="$PROJECT_ROOT/linker.ld"
    
    if [[ ! -f "$linker_script" ]]; then
        cat > "$linker_script" << 'EOF'
ENTRY(_start)

SECTIONS
{
    . = 1M;

    .text BLOCK(4K) : ALIGN(4K)
    {
        *(.multiboot)
        *(.text)
    }

    .rodata BLOCK(4K) : ALIGN(4K)
    {
        *(.rodata)
    }

    .data BLOCK(4K) : ALIGN(4K)
    {
        *(.data)
    }

    .bss BLOCK(4K) : ALIGN(4K)
    {
        *(COMMON)
        *(.bss)
    }
}
EOF
    fi
}

# Start QEMU with memory constraints
start_qemu_test() {
    local kernel_path="$1"
    
    log_info "Starting QEMU test with memory limit: $MEMORY_LIMIT"
    log_info "Architecture: $QEMU_ARCH"
    log_info "Kernel: $kernel_path"
    
    # Create log directory
    mkdir -p "$PROJECT_ROOT/qemu-logs"
    local log_file="$PROJECT_ROOT/qemu-logs/qemu-test-$(date +%Y%m%d-%H%M%S).log"
    
    # QEMU command with memory constraints
    local qemu_cmd=(
        "$QEMU_BINARY"
        -kernel "$kernel_path"
        -m "$MEMORY_LIMIT"
        -smp 1
        -machine pc-q35-2.0
        -cpu qemu64
        -nographic
        -no-reboot
        -no-shutdown
        -serial stdio
        -monitor none
        -append "console=ttyS0 panic=1 oops=panic"
        -netdev user,id=net0,restrict=on
        -device virtio-net-pci,netdev=net0
    )
    
    # Add memory debugging options
    qemu_cmd+=(
        -object memory-backend-ram,id=ram,size="$MEMORY_LIMIT"
        -numa node,memdev=ram
    )
    
    log_info "QEMU command: ${qemu_cmd[*]}"
    log_info "Log file: $log_file"
    
    # Start QEMU with timeout
    timeout 30s "${qemu_cmd[@]}" 2>&1 | tee "$log_file" || {
        local exit_code=$?
        if [[ $exit_code -eq 124 ]]; then
            log_success "QEMU test completed (timeout reached)"
        else
            log_warning "QEMU exited with code: $exit_code"
        fi
    }
    
    log_success "QEMU test finished. Log saved to: $log_file"
}

# Memory constraint validation
validate_memory_constraints() {
    log_info "Validating memory constraints..."
    
    # Check available system memory
    if command -v free >/dev/null 2>&1; then
        local available_mem=$(free -m | awk '/^Mem:/{print $7}')
        log_info "Available system memory: ${available_mem}MB"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        local total_mem=$(sysctl -n hw.memsize)
        local total_mem_mb=$((total_mem / 1024 / 1024))
        log_info "Total system memory: ${total_mem_mb}MB"
    fi
    
    # Validate memory limit
    local mem_value=$(echo "$MEMORY_LIMIT" | sed 's/[^0-9]//g')
    if [[ $mem_value -gt 250 ]]; then
        log_error "Memory limit exceeds 250MB: $MEMORY_LIMIT"
        exit 1
    fi
    
    log_success "Memory constraint validation passed: $MEMORY_LIMIT"
}

# Show usage
show_usage() {
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  -m, --memory   Set memory limit (default: 250M)"
    echo "  -a, --arch     Set architecture (default: x86_64)"
    echo "  -k, --kernel   Specify kernel path"
    echo ""
    echo "Examples:"
    echo "  $0                    # Run with default settings"
    echo "  $0 -m 128M           # Run with 128MB memory"
    echo "  $0 -k custom.bin     # Run with custom kernel"
}

# Main function
main() {
    local custom_kernel=""
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_usage
                exit 0
                ;;
            -m|--memory)
                MEMORY_LIMIT="$2"
                shift 2
                ;;
            -a|--arch)
                QEMU_ARCH="$2"
                QEMU_BINARY="qemu-system-$2"
                shift 2
                ;;
            -k|--kernel)
                custom_kernel="$2"
                shift 2
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    echo "OpenWrt Rust Bare-Metal QEMU Test Setup"
    echo "======================================="
    echo "Memory Limit: $MEMORY_LIMIT"
    echo "Architecture: $QEMU_ARCH"
    echo ""
    
    # Validate memory constraints
    validate_memory_constraints
    
    # Check QEMU installation
    check_qemu
    
    # Determine kernel path
    local kernel_path
    if [[ -n "$custom_kernel" ]]; then
        if [[ ! -f "$custom_kernel" ]]; then
            log_error "Custom kernel not found: $custom_kernel"
            exit 1
        fi
        kernel_path="$custom_kernel"
        log_info "Using custom kernel: $kernel_path"
    else
        # Create linker script and test kernel
        create_linker_script
        kernel_path=$(create_test_kernel)
    fi
    
    # Start QEMU test
    start_qemu_test "$kernel_path"
    
    log_success "QEMU test setup completed successfully"
}

# Execute main function
main "$@"
