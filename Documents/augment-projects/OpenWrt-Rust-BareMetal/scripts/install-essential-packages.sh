#!/bin/bash

# OpenWrt Essential Packages Installation Script
# Based on Phase 13 requirements from openwrt-rust-implementation-progress.md

echo "=========================================="
echo "OpenWrt Essential Packages Installation"
echo "=========================================="
echo ""

# Check if we're running on OpenWrt
if [ ! -f "/etc/openwrt_release" ]; then
    echo "⚠️  Warning: This script is designed for OpenWrt systems"
    echo "   Current system may not support opkg package manager"
    echo ""
fi

# Check if opkg is available
if ! command -v opkg >/dev/null 2>&1; then
    echo "❌ Error: opkg package manager not found"
    echo "   This script requires OpenWrt with opkg"
    exit 1
fi

echo "✅ OpenWrt system detected with opkg package manager"
echo ""

# Update package lists
echo "📦 Updating package lists..."
opkg update

if [ $? -ne 0 ]; then
    echo "❌ Failed to update package lists"
    exit 1
fi

echo "✅ Package lists updated successfully"
echo ""

# Define package categories and packages
declare -A PACKAGES

# Core Networking & Utilities
PACKAGES[networking]="ip-full firewall dnsmasq-full iperf3 tcpdump"

# Security & Encryption  
PACKAGES[security]="openssh-sftp-server ca-bundle stunnel"

# Monitoring & Debugging
PACKAGES[monitoring]="vnstat htop logread sysstat"

# Admin Essentials
PACKAGES[admin]="screen nano curl jq"

# Function to install packages in a category
install_category() {
    local category=$1
    local packages=${PACKAGES[$category]}
    
    echo "🔧 Installing $category packages..."
    echo "   Packages: $packages"
    echo ""
    
    for package in $packages; do
        echo "   Installing $package..."
        
        # Check if package is already installed
        if opkg list-installed | grep -q "^$package "; then
            echo "   ✅ $package is already installed"
        else
            # Install the package
            opkg install $package
            
            if [ $? -eq 0 ]; then
                echo "   ✅ $package installed successfully"
            else
                echo "   ⚠️  $package installation failed (may not be available in current repository)"
            fi
        fi
        echo ""
    done
}

# Install packages by category
echo "🌐 CORE NETWORKING & UTILITIES"
echo "================================"
install_category "networking"

echo "🔒 SECURITY & ENCRYPTION"
echo "========================="
install_category "security"

echo "📊 MONITORING & DEBUGGING"
echo "=========================="
install_category "monitoring"

echo "🛠️  ADMIN ESSENTIALS"
echo "===================="
install_category "admin"

echo "=========================================="
echo "INSTALLATION SUMMARY"
echo "=========================================="
echo ""

# Generate installation summary
echo "📋 Checking installed packages..."
echo ""

total_packages=0
installed_packages=0

for category in networking security monitoring admin; do
    echo "📂 $category packages:"
    packages=${PACKAGES[$category]}
    
    for package in $packages; do
        total_packages=$((total_packages + 1))
        
        if opkg list-installed | grep -q "^$package "; then
            version=$(opkg list-installed | grep "^$package " | awk '{print $3}')
            echo "   ✅ $package ($version)"
            installed_packages=$((installed_packages + 1))
        else
            echo "   ❌ $package (not installed)"
        fi
    done
    echo ""
done

# Calculate success rate
success_rate=$((installed_packages * 100 / total_packages))

echo "📊 INSTALLATION STATISTICS"
echo "=========================="
echo "Total packages: $total_packages"
echo "Successfully installed: $installed_packages"
echo "Success rate: $success_rate%"
echo ""

if [ $success_rate -ge 80 ]; then
    echo "🎉 Installation completed successfully!"
    echo "   Most essential packages are now available"
elif [ $success_rate -ge 50 ]; then
    echo "⚠️  Installation partially successful"
    echo "   Some packages may not be available in current repository"
else
    echo "❌ Installation had significant issues"
    echo "   Please check repository configuration and package availability"
fi

echo ""
echo "🔧 NEXT STEPS"
echo "============="
echo "1. Configure installed packages according to your requirements"
echo "2. Test package functionality using the validation scripts"
echo "3. Review package documentation for advanced configuration"
echo "4. Monitor system performance with installed monitoring tools"
echo ""

echo "📚 PACKAGE USAGE EXAMPLES"
echo "========================="
echo "• Network testing: iperf3 -s (server) / iperf3 -c <server_ip> (client)"
echo "• Traffic monitoring: vnstat -l (live view)"
echo "• Process monitoring: htop"
echo "• JSON processing: echo '{\"key\":\"value\"}' | jq ."
echo "• Secure tunneling: stunnel /etc/stunnel/stunnel.conf"
echo "• Terminal multiplexing: screen -S session_name"
echo ""

echo "✅ Essential packages installation script completed!"
echo "=========================================="
