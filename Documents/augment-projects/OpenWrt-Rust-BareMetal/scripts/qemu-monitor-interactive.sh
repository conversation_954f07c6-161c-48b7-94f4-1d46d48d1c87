#!/bin/bash

# OpenWrt Rust Firmware - QEMU Monitor Interactive Session
# This provides full interactive access to QEMU through the monitor interface

set -e

# Configuration
MEMORY_LIMIT="250M"
QEMU_BINARY="qemu-system-x86_64"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}🚀 OpenWrt Rust Firmware - QEMU Monitor Interactive Session${NC}"
echo ""

# Check if QEMU is available
if ! command -v $QEMU_BINARY &> /dev/null; then
    echo -e "${RED}[ERROR] QEMU not found: $QEMU_BINARY${NC}"
    echo "Please install QEMU first"
    exit 1
fi

echo -e "${GREEN}[SUCCESS] QEMU found: $QEMU_BINARY${NC}"

# Create a simple test environment
echo -e "${BLUE}[INFO] Creating interactive QEMU environment...${NC}"

# Create a log file
log_file="qemu-monitor-session-$(date +%Y%m%d_%H%M%S).log"

echo -e "${PURPLE}🔧 QEMU MONITOR INTERACTIVE SESSION${NC}"
echo ""
echo -e "${YELLOW}📋 INTERACTION GUIDE:${NC}"
echo "  • QEMU will start with monitor access"
echo "  • You can use QEMU monitor commands directly"
echo "  • Type 'help' to see all available commands"
echo "  • Type 'info registers' to see CPU state"
echo "  • Type 'info memory' to see memory usage"
echo "  • Type 'info cpus' to see CPU information"
echo "  • Type 'system_reset' to reset the system"
echo "  • Type 'quit' to exit QEMU"
echo ""

echo -e "${PURPLE}🔧 QEMU CONFIGURATION:${NC}"
echo "  • Memory: $MEMORY_LIMIT (enforced limit)"
echo "  • Architecture: x86_64"
echo "  • Machine: Q35 (modern PC)"
echo "  • Monitor: Direct access (this terminal)"
echo "  • Network: User mode (restricted)"
echo ""

echo -e "${PURPLE}🚀 MONITOR FEATURES:${NC}"
echo "  • Full system control"
echo "  • Memory inspection"
echo "  • CPU state monitoring"
echo "  • Device management"
echo "  • System debugging"
echo ""

echo -e "${CYAN}Press Enter to start QEMU Monitor, or Ctrl+C to cancel...${NC}"
read

echo ""
echo -e "${GREEN}[INFO] Starting QEMU Monitor Interactive Session...${NC}"
echo ""

echo -e "${YELLOW}[MONITOR] QEMU Command:${NC}"
echo ""
cat << EOF
$QEMU_BINARY \\
  -m $MEMORY_LIMIT \\
  -smp 1 \\
  -machine q35 \\
  -cpu qemu64 \\
  -nographic \\
  -monitor stdio \\
  -serial none \\
  -netdev user,id=net0,restrict=on \\
  -device virtio-net-pci,netdev=net0 \\
  -object memory-backend-ram,id=ram,size=$MEMORY_LIMIT \\
  -numa node,memdev=ram
EOF

echo ""
echo "=========================================="
echo -e "${GREEN}[MONITOR] QEMU INTERACTIVE SESSION STARTING${NC}"
echo "=========================================="
echo ""
echo -e "${CYAN}[INFO] You now have direct access to QEMU monitor${NC}"
echo -e "${CYAN}[INFO] Type 'help' for available commands${NC}"
echo -e "${CYAN}[INFO] Type 'quit' to exit${NC}"
echo ""

# Run QEMU with monitor access
$QEMU_BINARY \
    -m $MEMORY_LIMIT \
    -smp 1 \
    -machine q35 \
    -cpu qemu64 \
    -nographic \
    -monitor stdio \
    -serial none \
    -netdev user,id=net0,restrict=on \
    -device virtio-net-pci,netdev=net0 \
    -object memory-backend-ram,id=ram,size=$MEMORY_LIMIT \
    -numa node,memdev=ram \
    2>&1 | tee "$log_file"

echo ""
echo -e "${GREEN}[SUCCESS] QEMU Monitor session completed${NC}"
echo -e "${BLUE}[INFO] Session logged to: $log_file${NC}"
echo ""
echo -e "${PURPLE}📊 SESSION SUMMARY:${NC}"
echo "  • Monitor commands executed"
echo "  • System state inspected"
echo "  • Memory usage monitored"
echo "  • Interactive session completed"
echo ""
echo -e "${CYAN}🎉 OpenWrt Rust Firmware - Monitor Session Complete!${NC}"
