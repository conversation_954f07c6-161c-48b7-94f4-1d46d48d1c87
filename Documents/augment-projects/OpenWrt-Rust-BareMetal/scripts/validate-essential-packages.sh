#!/bin/bash

# OpenWrt Essential Packages Validation Script
# Tests functionality of installed essential packages

echo "=========================================="
echo "OpenWrt Essential Packages Validation"
echo "=========================================="
echo ""

# Check if we're running on OpenWrt
if [ ! -f "/etc/openwrt_release" ]; then
    echo "⚠️  Warning: This script is designed for OpenWrt systems"
    echo ""
fi

# Function to test command availability and basic functionality
test_command() {
    local cmd=$1
    local test_args=$2
    local description=$3
    
    echo "🔍 Testing $cmd - $description"
    
    if command -v $cmd >/dev/null 2>&1; then
        echo "   ✅ Command available: $(which $cmd)"
        
        # Run basic test if test_args provided
        if [ -n "$test_args" ]; then
            echo "   🧪 Running basic test: $cmd $test_args"
            timeout 5 $cmd $test_args >/dev/null 2>&1
            if [ $? -eq 0 ] || [ $? -eq 124 ]; then  # 124 is timeout
                echo "   ✅ Basic functionality test passed"
            else
                echo "   ⚠️  Basic functionality test failed"
            fi
        fi
    else
        echo "   ❌ Command not available"
    fi
    echo ""
}

# Function to test package installation
test_package() {
    local package=$1
    local description=$2
    
    echo "📦 Testing package: $package - $description"
    
    if opkg list-installed | grep -q "^$package "; then
        version=$(opkg list-installed | grep "^$package " | awk '{print $3}')
        echo "   ✅ Package installed: $package ($version)"
    else
        echo "   ❌ Package not installed: $package"
    fi
    echo ""
}

echo "🌐 CORE NETWORKING & UTILITIES VALIDATION"
echo "=========================================="

test_package "ip-full" "Advanced IP routing"
test_command "ip" "addr show" "IP address management"

test_package "firewall" "Packet filtering"
test_command "fw3" "--help" "Firewall management"

test_package "dnsmasq-full" "DHCP/DNS server"
test_command "dnsmasq" "--version" "DNS/DHCP service"

test_package "iperf3" "Network bandwidth testing"
test_command "iperf3" "--version" "Network performance testing"

test_package "tcpdump" "Network packet analysis"
test_command "tcpdump" "--version" "Packet capture"

echo "🔒 SECURITY & ENCRYPTION VALIDATION"
echo "===================================="

test_package "openssh-sftp-server" "Secure file transfers"
test_command "sftp-server" "--help" "SFTP server"

test_package "ca-bundle" "SSL certificates"
if [ -f "/etc/ssl/certs/ca-certificates.crt" ]; then
    echo "📦 Testing package: ca-bundle - SSL certificates"
    echo "   ✅ CA bundle file found: /etc/ssl/certs/ca-certificates.crt"
    cert_count=$(grep -c "BEGIN CERTIFICATE" /etc/ssl/certs/ca-certificates.crt 2>/dev/null || echo "0")
    echo "   📊 Certificate count: $cert_count"
    echo ""
fi

test_package "stunnel" "TLS/SSL tunneling"
test_command "stunnel" "-version" "SSL tunnel"

echo "📊 MONITORING & DEBUGGING VALIDATION"
echo "====================================="

test_package "vnstat" "Traffic monitoring"
test_command "vnstat" "--version" "Network statistics"

test_package "htop" "Process viewer"
test_command "htop" "--version" "Process monitoring"

test_package "logread" "System logs"
test_command "logread" "-h" "Log reading"

test_package "sysstat" "System monitoring"
test_command "iostat" "-V" "I/O statistics"

echo "🛠️  ADMIN ESSENTIALS VALIDATION"
echo "==============================="

test_package "screen" "Terminal multiplexer"
test_command "screen" "-version" "Terminal sessions"

test_package "nano" "Text editor"
test_command "nano" "--version" "Text editing"

test_package "curl" "HTTP/HTTPS tool"
test_command "curl" "--version" "HTTP client"

test_package "jq" "JSON processor"
test_command "jq" "--version" "JSON processing"

echo "=========================================="
echo "FUNCTIONALITY TESTS"
echo "=========================================="
echo ""

echo "🧪 Running advanced functionality tests..."
echo ""

# Test jq JSON processing
echo "🔧 Testing jq JSON processing..."
if command -v jq >/dev/null 2>&1; then
    echo '{"test": "value", "number": 42}' | jq '.test' >/dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "   ✅ jq JSON processing works correctly"
    else
        echo "   ❌ jq JSON processing failed"
    fi
else
    echo "   ❌ jq not available for testing"
fi
echo ""

# Test curl connectivity (if network available)
echo "🔧 Testing curl HTTP functionality..."
if command -v curl >/dev/null 2>&1; then
    timeout 10 curl -s --head http://httpbin.org/get >/dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "   ✅ curl HTTP connectivity works"
    else
        echo "   ⚠️  curl HTTP test failed (may be network/firewall related)"
    fi
else
    echo "   ❌ curl not available for testing"
fi
echo ""

# Test vnstat database
echo "🔧 Testing vnstat database..."
if command -v vnstat >/dev/null 2>&1; then
    vnstat --dbdir /var/lib/vnstat --iflist >/dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "   ✅ vnstat database accessible"
    else
        echo "   ⚠️  vnstat database may need initialization"
    fi
else
    echo "   ❌ vnstat not available for testing"
fi
echo ""

echo "=========================================="
echo "VALIDATION SUMMARY"
echo "=========================================="
echo ""

# Count installed packages
total_packages=16
installed_count=0

packages="ip-full firewall dnsmasq-full iperf3 tcpdump openssh-sftp-server ca-bundle stunnel vnstat htop logread sysstat screen nano curl jq"

for package in $packages; do
    if opkg list-installed | grep -q "^$package "; then
        installed_count=$((installed_count + 1))
    fi
done

success_rate=$((installed_count * 100 / total_packages))

echo "📊 PACKAGE INSTALLATION STATUS"
echo "=============================="
echo "Total essential packages: $total_packages"
echo "Successfully installed: $installed_count"
echo "Installation success rate: $success_rate%"
echo ""

if [ $success_rate -ge 90 ]; then
    echo "🎉 Excellent! Almost all essential packages are installed and ready"
elif [ $success_rate -ge 75 ]; then
    echo "✅ Good! Most essential packages are installed"
elif [ $success_rate -ge 50 ]; then
    echo "⚠️  Moderate! Some essential packages are missing"
else
    echo "❌ Poor! Many essential packages are missing"
fi

echo ""
echo "🔧 RECOMMENDATIONS"
echo "=================="

if [ $success_rate -lt 100 ]; then
    echo "• Run 'opkg update' to refresh package lists"
    echo "• Check repository configuration in /etc/opkg.conf"
    echo "• Some packages may not be available for your architecture"
    echo "• Consider alternative packages for missing functionality"
fi

echo "• Configure installed packages according to your needs"
echo "• Set up monitoring with vnstat and htop"
echo "• Test network functionality with iperf3 and tcpdump"
echo "• Use screen for long-running tasks"
echo ""

echo "✅ Essential packages validation completed!"
echo "=========================================="
