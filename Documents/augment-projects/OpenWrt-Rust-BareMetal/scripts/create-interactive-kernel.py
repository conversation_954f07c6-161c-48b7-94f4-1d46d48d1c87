#!/usr/bin/env python3
"""
Create an interactive kernel that works with QEMU
"""

import struct

def create_interactive_kernel():
    """Create a kernel with interactive shell functionality"""
    
    # Create a simple flat binary kernel
    kernel = bytearray(8192)  # 8KB kernel
    
    # Multiboot header (must be at the beginning)
    multiboot_header = [
        0x02, 0xB0, 0xAD, 0x1B,  # Multiboot magic number (0x1BADB002)
        0x00, 0x00, 0x00, 0x00,  # Flags
        0xFE, 0x4F, 0x52, 0xE4,  # Checksum (-(magic + flags))
    ]

    # x86 machine code for interactive shell
    code = [
        # Initialize serial port (COM1 = 0x3F8)
        0xBA, 0xF8, 0x03,  # mov dx, 0x3F8 (COM1 base)
        
        # Initialize serial port properly
        0xB0, 0x00,        # mov al, 0x00
        0x42,              # inc dx (0x3F9 - interrupt enable)
        0xEE,              # out dx, al
        0x4A,              # dec dx (back to 0x3F8)
        
        # Send boot message
        0xB0, 0x0D,        # mov al, '\r'
        0xEE,              # out dx, al
        0xB0, 0x0A,        # mov al, '\n'
        0xEE,              # out dx, al
        
        # Send "OpenWrt Rust Interactive Kernel"
        0xB0, 0x4F,        # mov al, 'O'
        0xEE,              # out dx, al
        0xB0, 0x70,        # mov al, 'p'
        0xEE,              # out dx, al
        0xB0, 0x65,        # mov al, 'e'
        0xEE,              # out dx, al
        0xB0, 0x6E,        # mov al, 'n'
        0xEE,              # out dx, al
        0xB0, 0x57,        # mov al, 'W'
        0xEE,              # out dx, al
        0xB0, 0x72,        # mov al, 'r'
        0xEE,              # out dx, al
        0xB0, 0x74,        # mov al, 't'
        0xEE,              # out dx, al
        0xB0, 0x20,        # mov al, ' '
        0xEE,              # out dx, al
        0xB0, 0x52,        # mov al, 'R'
        0xEE,              # out dx, al
        0xB0, 0x75,        # mov al, 'u'
        0xEE,              # out dx, al
        0xB0, 0x73,        # mov al, 's'
        0xEE,              # out dx, al
        0xB0, 0x74,        # mov al, 't'
        0xEE,              # out dx, al
        0xB0, 0x20,        # mov al, ' '
        0xEE,              # out dx, al
        0xB0, 0x49,        # mov al, 'I'
        0xEE,              # out dx, al
        0xB0, 0x6E,        # mov al, 'n'
        0xEE,              # out dx, al
        0xB0, 0x74,        # mov al, 't'
        0xEE,              # out dx, al
        0xB0, 0x65,        # mov al, 'e'
        0xEE,              # out dx, al
        0xB0, 0x72,        # mov al, 'r'
        0xEE,              # out dx, al
        0xB0, 0x61,        # mov al, 'a'
        0xEE,              # out dx, al
        0xB0, 0x63,        # mov al, 'c'
        0xEE,              # out dx, al
        0xB0, 0x74,        # mov al, 't'
        0xEE,              # out dx, al
        0xB0, 0x69,        # mov al, 'i'
        0xEE,              # out dx, al
        0xB0, 0x76,        # mov al, 'v'
        0xEE,              # out dx, al
        0xB0, 0x65,        # mov al, 'e'
        0xEE,              # out dx, al
        0xB0, 0x20,        # mov al, ' '
        0xEE,              # out dx, al
        0xB0, 0x4B,        # mov al, 'K'
        0xEE,              # out dx, al
        0xB0, 0x65,        # mov al, 'e'
        0xEE,              # out dx, al
        0xB0, 0x72,        # mov al, 'r'
        0xEE,              # out dx, al
        0xB0, 0x6E,        # mov al, 'n'
        0xEE,              # out dx, al
        0xB0, 0x65,        # mov al, 'e'
        0xEE,              # out dx, al
        0xB0, 0x6C,        # mov al, 'l'
        0xEE,              # out dx, al
        0xB0, 0x0D,        # mov al, '\r'
        0xEE,              # out dx, al
        0xB0, 0x0A,        # mov al, '\n'
        0xEE,              # out dx, al
        
        # Send system info
        0xB0, 0x56,        # mov al, 'V'
        0xEE,              # out dx, al
        0xB0, 0x65,        # mov al, 'e'
        0xEE,              # out dx, al
        0xB0, 0x72,        # mov al, 'r'
        0xEE,              # out dx, al
        0xB0, 0x73,        # mov al, 's'
        0xEE,              # out dx, al
        0xB0, 0x69,        # mov al, 'i'
        0xEE,              # out dx, al
        0xB0, 0x6F,        # mov al, 'o'
        0xEE,              # out dx, al
        0xB0, 0x6E,        # mov al, 'n'
        0xEE,              # out dx, al
        0xB0, 0x3A,        # mov al, ':'
        0xEE,              # out dx, al
        0xB0, 0x20,        # mov al, ' '
        0xEE,              # out dx, al
        0xB0, 0x31,        # mov al, '1'
        0xEE,              # out dx, al
        0xB0, 0x2E,        # mov al, '.'
        0xEE,              # out dx, al
        0xB0, 0x30,        # mov al, '0'
        0xEE,              # out dx, al
        0xB0, 0x2E,        # mov al, '.'
        0xEE,              # out dx, al
        0xB0, 0x30,        # mov al, '0'
        0xEE,              # out dx, al
        0xB0, 0x0D,        # mov al, '\r'
        0xEE,              # out dx, al
        0xB0, 0x0A,        # mov al, '\n'
        0xEE,              # out dx, al
        
        # Send memory info
        0xB0, 0x4D,        # mov al, 'M'
        0xEE,              # out dx, al
        0xB0, 0x65,        # mov al, 'e'
        0xEE,              # out dx, al
        0xB0, 0x6D,        # mov al, 'm'
        0xEE,              # out dx, al
        0xB0, 0x6F,        # mov al, 'o'
        0xEE,              # out dx, al
        0xB0, 0x72,        # mov al, 'r'
        0xEE,              # out dx, al
        0xB0, 0x79,        # mov al, 'y'
        0xEE,              # out dx, al
        0xB0, 0x3A,        # mov al, ':'
        0xEE,              # out dx, al
        0xB0, 0x20,        # mov al, ' '
        0xEE,              # out dx, al
        0xB0, 0x32,        # mov al, '2'
        0xEE,              # out dx, al
        0xB0, 0x35,        # mov al, '5'
        0xEE,              # out dx, al
        0xB0, 0x30,        # mov al, '0'
        0xEE,              # out dx, al
        0xB0, 0x4D,        # mov al, 'M'
        0xEE,              # out dx, al
        0xB0, 0x42,        # mov al, 'B'
        0xEE,              # out dx, al
        0xB0, 0x0D,        # mov al, '\r'
        0xEE,              # out dx, al
        0xB0, 0x0A,        # mov al, '\n'
        0xEE,              # out dx, al
        0xB0, 0x0D,        # mov al, '\r'
        0xEE,              # out dx, al
        0xB0, 0x0A,        # mov al, '\n'
        0xEE,              # out dx, al
        
        # Interactive shell prompt loop
        # MAIN_LOOP:
        # Send prompt "openwrt> "
        0xB0, 0x6F,        # mov al, 'o'
        0xEE,              # out dx, al
        0xB0, 0x70,        # mov al, 'p'
        0xEE,              # out dx, al
        0xB0, 0x65,        # mov al, 'e'
        0xEE,              # out dx, al
        0xB0, 0x6E,        # mov al, 'n'
        0xEE,              # out dx, al
        0xB0, 0x77,        # mov al, 'w'
        0xEE,              # out dx, al
        0xB0, 0x72,        # mov al, 'r'
        0xEE,              # out dx, al
        0xB0, 0x74,        # mov al, 't'
        0xEE,              # out dx, al
        0xB0, 0x3E,        # mov al, '>'
        0xEE,              # out dx, al
        0xB0, 0x20,        # mov al, ' '
        0xEE,              # out dx, al
        
        # INPUT_LOOP: Wait for input
        0x42,              # inc dx (0x3F9 - line status register)
        0x42,              # inc dx (0x3FA)
        0x42,              # inc dx (0x3FB)
        0x42,              # inc dx (0x3FC)
        0x42,              # inc dx (0x3FD - line status register)
        0xEC,              # in al, dx (read line status)
        0xA8, 0x01,        # test al, 1 (check if data available)
        0x74, 0xFB,        # jz INPUT_LOOP (jump back if no data)
        
        # Data available, read it
        0x4A,              # dec dx (back to 0x3FC)
        0x4A,              # dec dx (back to 0x3FB)
        0x4A,              # dec dx (back to 0x3FA)
        0x4A,              # dec dx (back to 0x3F9)
        0x4A,              # dec dx (back to 0x3F8 - data register)
        0xEC,              # in al, dx (read character)
        
        # Echo the character back
        0xEE,              # out dx, al
        
        # Check if it's Enter (0x0D)
        0x3C, 0x0D,        # cmp al, 0x0D
        0x74, 0x0A,        # je HANDLE_ENTER
        
        # Check if it's newline (0x0A)
        0x3C, 0x0A,        # cmp al, 0x0A
        0x74, 0x06,        # je HANDLE_ENTER
        
        # Regular character, continue input loop
        0xEB, 0xE1,        # jmp INPUT_LOOP
        
        # HANDLE_ENTER:
        # Send newline
        0xB0, 0x0D,        # mov al, '\r'
        0xEE,              # out dx, al
        0xB0, 0x0A,        # mov al, '\n'
        0xEE,              # out dx, al
        
        # Send response message
        0xB0, 0x43,        # mov al, 'C'
        0xEE,              # out dx, al
        0xB0, 0x6F,        # mov al, 'o'
        0xEE,              # out dx, al
        0xB0, 0x6D,        # mov al, 'm'
        0xEE,              # out dx, al
        0xB0, 0x6D,        # mov al, 'm'
        0xEE,              # out dx, al
        0xB0, 0x61,        # mov al, 'a'
        0xEE,              # out dx, al
        0xB0, 0x6E,        # mov al, 'n'
        0xEE,              # out dx, al
        0xB0, 0x64,        # mov al, 'd'
        0xEE,              # out dx, al
        0xB0, 0x20,        # mov al, ' '
        0xEE,              # out dx, al
        0xB0, 0x72,        # mov al, 'r'
        0xEE,              # out dx, al
        0xB0, 0x65,        # mov al, 'e'
        0xEE,              # out dx, al
        0xB0, 0x63,        # mov al, 'c'
        0xEE,              # out dx, al
        0xB0, 0x65,        # mov al, 'e'
        0xEE,              # out dx, al
        0xB0, 0x69,        # mov al, 'i'
        0xEE,              # out dx, al
        0xB0, 0x76,        # mov al, 'v'
        0xEE,              # out dx, al
        0xB0, 0x65,        # mov al, 'e'
        0xEE,              # out dx, al
        0xB0, 0x64,        # mov al, 'd'
        0xEE,              # out dx, al
        0xB0, 0x21,        # mov al, '!'
        0xEE,              # out dx, al
        0xB0, 0x0D,        # mov al, '\r'
        0xEE,              # out dx, al
        0xB0, 0x0A,        # mov al, '\n'
        0xEE,              # out dx, al
        
        # Jump back to main loop
        0xEB, 0xB5,        # jmp MAIN_LOOP
        
        # Padding and halt (should never reach here)
        0xFA,              # cli (disable interrupts)
        0xF4,              # hlt (halt)
        0xEB, 0xFD,        # jmp $ (infinite loop)
    ]
    
    # Copy multiboot header first
    for i, byte_val in enumerate(multiboot_header):
        if i < len(kernel):
            kernel[i] = byte_val

    # Copy code after multiboot header
    offset = len(multiboot_header)
    for i, byte_val in enumerate(code):
        if offset + i < len(kernel):
            kernel[offset + i] = byte_val
    
    # Write kernel to file
    with open("interactive-kernel.bin", "wb") as f:
        f.write(kernel)
    
    print("Interactive kernel created: interactive-kernel.bin")
    print(f"Kernel size: {len(kernel)} bytes")
    print("This kernel provides an interactive shell via COM1 serial port")
    print("Features:")
    print("  - Boot messages")
    print("  - Interactive prompt")
    print("  - Character echo")
    print("  - Command processing")

if __name__ == "__main__":
    create_interactive_kernel()
