#!/usr/bin/env python3
"""
Create a simple kernel that outputs to serial console for QEMU testing
"""

import struct

def create_serial_kernel():
    """Create a kernel that outputs to COM1 serial port"""
    
    # Create a simple flat binary kernel
    kernel = bytearray(4096)
    
    # x86 machine code to output to serial port
    code = [
        # Initialize serial port (COM1 = 0x3F8)
        0xBA, 0xF8, 0x03,  # mov dx, 0x3F8 (COM1 base)
        
        # Send message character by character
        0xB0, 0x4F,        # mov al, 'O'
        0xEE,              # out dx, al
        
        0xB0, 0x70,        # mov al, 'p'
        0xEE,              # out dx, al
        
        0xB0, 0x65,        # mov al, 'e'
        0xEE,              # out dx, al
        
        0xB0, 0x6E,        # mov al, 'n'
        0xEE,              # out dx, al
        
        0xB0, 0x57,        # mov al, 'W'
        0xEE,              # out dx, al
        
        0xB0, 0x72,        # mov al, 'r'
        0xEE,              # out dx, al
        
        0xB0, 0x74,        # mov al, 't'
        0xEE,              # out dx, al
        
        0xB0, 0x20,        # mov al, ' '
        0xEE,              # out dx, al
        
        0xB0, 0x52,        # mov al, 'R'
        0xEE,              # out dx, al
        
        0xB0, 0x75,        # mov al, 'u'
        0xEE,              # out dx, al
        
        0xB0, 0x73,        # mov al, 's'
        0xEE,              # out dx, al
        
        0xB0, 0x74,        # mov al, 't'
        0xEE,              # out dx, al
        
        0xB0, 0x20,        # mov al, ' '
        0xEE,              # out dx, al
        
        0xB0, 0x4B,        # mov al, 'K'
        0xEE,              # out dx, al
        
        0xB0, 0x65,        # mov al, 'e'
        0xEE,              # out dx, al
        
        0xB0, 0x72,        # mov al, 'r'
        0xEE,              # out dx, al
        
        0xB0, 0x6E,        # mov al, 'n'
        0xEE,              # out dx, al
        
        0xB0, 0x65,        # mov al, 'e'
        0xEE,              # out dx, al
        
        0xB0, 0x6C,        # mov al, 'l'
        0xEE,              # out dx, al
        
        0xB0, 0x20,        # mov al, ' '
        0xEE,              # out dx, al
        
        0xB0, 0x2D,        # mov al, '-'
        0xEE,              # out dx, al
        
        0xB0, 0x20,        # mov al, ' '
        0xEE,              # out dx, al
        
        0xB0, 0x32,        # mov al, '2'
        0xEE,              # out dx, al
        
        0xB0, 0x35,        # mov al, '5'
        0xEE,              # out dx, al
        
        0xB0, 0x30,        # mov al, '0'
        0xEE,              # out dx, al
        
        0xB0, 0x4D,        # mov al, 'M'
        0xEE,              # out dx, al
        
        0xB0, 0x42,        # mov al, 'B'
        0xEE,              # out dx, al
        
        0xB0, 0x20,        # mov al, ' '
        0xEE,              # out dx, al
        
        0xB0, 0x54,        # mov al, 'T'
        0xEE,              # out dx, al
        
        0xB0, 0x65,        # mov al, 'e'
        0xEE,              # out dx, al
        
        0xB0, 0x73,        # mov al, 's'
        0xEE,              # out dx, al
        
        0xB0, 0x74,        # mov al, 't'
        0xEE,              # out dx, al
        
        0xB0, 0x0D,        # mov al, '\r'
        0xEE,              # out dx, al
        
        0xB0, 0x0A,        # mov al, '\n'
        0xEE,              # out dx, al
        
        # Send success message
        0xB0, 0x53,        # mov al, 'S'
        0xEE,              # out dx, al
        
        0xB0, 0x75,        # mov al, 'u'
        0xEE,              # out dx, al
        
        0xB0, 0x63,        # mov al, 'c'
        0xEE,              # out dx, al
        
        0xB0, 0x63,        # mov al, 'c'
        0xEE,              # out dx, al
        
        0xB0, 0x65,        # mov al, 'e'
        0xEE,              # out dx, al
        
        0xB0, 0x73,        # mov al, 's'
        0xEE,              # out dx, al
        
        0xB0, 0x73,        # mov al, 's'
        0xEE,              # out dx, al
        
        0xB0, 0x3A,        # mov al, ':'
        0xEE,              # out dx, al
        
        0xB0, 0x20,        # mov al, ' '
        0xEE,              # out dx, al
        
        0xB0, 0x51,        # mov al, 'Q'
        0xEE,              # out dx, al
        
        0xB0, 0x45,        # mov al, 'E'
        0xEE,              # out dx, al
        
        0xB0, 0x4D,        # mov al, 'M'
        0xEE,              # out dx, al
        
        0xB0, 0x55,        # mov al, 'U'
        0xEE,              # out dx, al
        
        0xB0, 0x20,        # mov al, ' '
        0xEE,              # out dx, al
        
        0xB0, 0x62,        # mov al, 'b'
        0xEE,              # out dx, al
        
        0xB0, 0x6F,        # mov al, 'o'
        0xEE,              # out dx, al
        
        0xB0, 0x6F,        # mov al, 'o'
        0xEE,              # out dx, al
        
        0xB0, 0x74,        # mov al, 't'
        0xEE,              # out dx, al
        
        0xB0, 0x20,        # mov al, ' '
        0xEE,              # out dx, al
        
        0xB0, 0x74,        # mov al, 't'
        0xEE,              # out dx, al
        
        0xB0, 0x65,        # mov al, 'e'
        0xEE,              # out dx, al
        
        0xB0, 0x73,        # mov al, 's'
        0xEE,              # out dx, al
        
        0xB0, 0x74,        # mov al, 't'
        0xEE,              # out dx, al
        
        0xB0, 0x20,        # mov al, ' '
        0xEE,              # out dx, al
        
        0xB0, 0x50,        # mov al, 'P'
        0xEE,              # out dx, al
        
        0xB0, 0x41,        # mov al, 'A'
        0xEE,              # out dx, al
        
        0xB0, 0x53,        # mov al, 'S'
        0xEE,              # out dx, al
        
        0xB0, 0x53,        # mov al, 'S'
        0xEE,              # out dx, al
        
        0xB0, 0x45,        # mov al, 'E'
        0xEE,              # out dx, al
        
        0xB0, 0x44,        # mov al, 'D'
        0xEE,              # out dx, al
        
        0xB0, 0x0D,        # mov al, '\r'
        0xEE,              # out dx, al
        
        0xB0, 0x0A,        # mov al, '\n'
        0xEE,              # out dx, al
        
        # Halt the CPU
        0xFA,              # cli (disable interrupts)
        0xF4,              # hlt (halt)
        0xEB, 0xFD,        # jmp $ (infinite loop)
    ]
    
    # Copy code to kernel
    for i, byte_val in enumerate(code):
        if i < len(kernel):
            kernel[i] = byte_val
    
    # Write kernel to file
    with open("serial-kernel.bin", "wb") as f:
        f.write(kernel)
    
    print("Serial kernel created: serial-kernel.bin")
    print(f"Kernel size: {len(kernel)} bytes")
    print("This kernel will output to COM1 serial port")

if __name__ == "__main__":
    create_serial_kernel()
