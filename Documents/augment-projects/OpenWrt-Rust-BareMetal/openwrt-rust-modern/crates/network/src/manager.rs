//! Network manager implementation
//!
//! The NetworkManager is the central coordinator for all network operations,
//! managing interfaces, protocols, routing, and events.

use crate::{InterfaceManager, ProtocolManager, RouteManager, NetworkEvent};
use openwrt_core::{Error, Result, Configurable, Lifecycle, Monitorable, ComponentStatus};
use openwrt_config::schema::{NetworkConfig, InterfaceConfig};
use serde::{Deserialize, Serialize};
use heapless::{Vec as HeaplessVec, FnvIndexMap, String as HeaplessString};

/// Network manager configuration
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct NetworkManagerConfig {
    /// Enable IPv6 support
    pub ipv6_enabled: bool,
    /// Enable IP forwarding
    pub ip_forwarding: bool,
    /// Default interface metric
    pub default_metric: u32,
    /// Network event buffer size
    pub event_buffer_size: usize,
    /// Interface scan interval in seconds
    pub scan_interval: u64,
}

impl Default for NetworkManagerConfig {
    fn default() -> Self {
        Self {
            ipv6_enabled: true,
            ip_forwarding: false,
            default_metric: 100,
            event_buffer_size: 256,
            scan_interval: 30,
        }
    }
}

/// Network manager health information
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct NetworkHealthInfo {
    /// Number of active interfaces
    pub active_interfaces: u32,
    /// Number of configured routes
    pub configured_routes: u32,
    /// Total network errors
    pub total_errors: u64,
    /// Last error timestamp
    pub last_error: Option<u64>,
    /// System uptime
    pub uptime: u64,
}

/// Main network manager
pub struct NetworkManager {
    /// Manager configuration
    config: NetworkManagerConfig,
    /// Interface manager
    interface_manager: InterfaceManager,
    /// Protocol manager
    protocol_manager: ProtocolManager,
    /// Route manager
    route_manager: RouteManager,
    /// Current status
    status: ComponentStatus,
    /// Network configuration
    network_config: Option<NetworkConfig>,
    /// Event handlers
    event_handlers: FnvIndexMap<u64, Box<dyn NetworkEventHandler>, 32>,
    /// Next handler ID
    next_handler_id: u64,
    /// Error count
    error_count: u64,
    /// Start time
    start_time: Option<u64>,
}

/// Network event handler trait
pub trait NetworkEventHandler: Send + Sync {
    /// Handle a network event
    fn handle_event(&mut self, event: &NetworkEvent) -> Result<()>;
    
    /// Check if this handler can process the event
    fn can_handle(&self, event: &NetworkEvent) -> bool;
    
    /// Get handler priority
    fn priority(&self) -> openwrt_core::Priority {
        openwrt_core::Priority::Normal
    }
}

impl NetworkManager {
    /// Create a new network manager
    pub fn new() -> Self {
        Self {
            config: NetworkManagerConfig::default(),
            interface_manager: InterfaceManager::new(),
            protocol_manager: ProtocolManager::new(),
            route_manager: RouteManager::new(),
            status: ComponentStatus::Uninitialized,
            network_config: None,
            event_handlers: FnvIndexMap::new(),
            next_handler_id: 1,
            error_count: 0,
            start_time: None,
        }
    }

    /// Create a new network manager with custom configuration
    pub fn with_config(config: NetworkManagerConfig) -> Self {
        Self {
            config,
            interface_manager: InterfaceManager::new(),
            protocol_manager: ProtocolManager::new(),
            route_manager: RouteManager::new(),
            status: ComponentStatus::Uninitialized,
            network_config: None,
            event_handlers: FnvIndexMap::new(),
            next_handler_id: 1,
            error_count: 0,
            start_time: None,
        }
    }

    /// Configure a network interface
    pub fn configure_interface(&mut self, config: &InterfaceConfig) -> Result<()> {
        log::info!("Configuring interface: {}", config.name);
        
        // Configure the interface
        self.interface_manager.configure_interface(config)?;
        
        // Set up protocol handler if needed
        if let Some(protocol_config) = &config.protocol {
            self.protocol_manager.configure_protocol(&config.name, protocol_config)?;
        }
        
        // Emit configuration event
        let event = NetworkEvent::InterfaceConfigured {
            interface: config.name.clone(),
            success: true,
        };
        self.emit_event(event)?;
        
        Ok(())
    }

    /// Start a network interface
    pub fn start_interface(&mut self, interface_name: &str) -> Result<()> {
        log::info!("Starting interface: {}", interface_name);
        
        // Start the interface
        self.interface_manager.start_interface(interface_name)?;
        
        // Start protocol handler if configured
        if self.protocol_manager.has_protocol(interface_name) {
            self.protocol_manager.start_protocol(interface_name)?;
        }
        
        // Emit start event
        let event = NetworkEvent::InterfaceStarted {
            interface: HeaplessString::try_from(interface_name).map_err(|_| {
                Error::InvalidInput("Interface name too long".to_string())
            })?,
        };
        self.emit_event(event)?;
        
        Ok(())
    }

    /// Stop a network interface
    pub fn stop_interface(&mut self, interface_name: &str) -> Result<()> {
        log::info!("Stopping interface: {}", interface_name);
        
        // Stop protocol handler first
        if self.protocol_manager.has_protocol(interface_name) {
            self.protocol_manager.stop_protocol(interface_name)?;
        }
        
        // Stop the interface
        self.interface_manager.stop_interface(interface_name)?;
        
        // Emit stop event
        let event = NetworkEvent::InterfaceStopped {
            interface: HeaplessString::try_from(interface_name).map_err(|_| {
                Error::InvalidInput("Interface name too long".to_string())
            })?,
            reason: HeaplessString::try_from("Manual stop").unwrap(),
        };
        self.emit_event(event)?;
        
        Ok(())
    }

    /// Get interface status
    pub fn get_interface_status(&self, interface_name: &str) -> Result<crate::InterfaceState> {
        self.interface_manager.get_interface_status(interface_name)
    }

    /// List all configured interfaces
    pub fn list_interfaces(&self) -> HeaplessVec<HeaplessString<32>, 32> {
        self.interface_manager.list_interfaces()
    }

    /// Add a route
    pub fn add_route(&mut self, route: &crate::Route) -> Result<()> {
        log::info!("Adding route: {:?}", route);
        self.route_manager.add_route(route.clone())?;
        
        // Emit route event
        let event = NetworkEvent::RouteAdded {
            destination: route.destination.clone(),
            gateway: route.gateway.clone(),
        };
        self.emit_event(event)?;
        
        Ok(())
    }

    /// Remove a route
    pub fn remove_route(&mut self, destination: &str) -> Result<()> {
        log::info!("Removing route: {}", destination);
        self.route_manager.remove_route(destination)?;
        
        // Emit route event
        let event = NetworkEvent::RouteRemoved {
            destination: HeaplessString::try_from(destination).map_err(|_| {
                Error::InvalidInput("Destination too long".to_string())
            })?,
        };
        self.emit_event(event)?;
        
        Ok(())
    }

    /// Register an event handler
    pub fn register_event_handler(&mut self, handler: Box<dyn NetworkEventHandler>) -> Result<u64> {
        let handler_id = self.next_handler_id;
        self.next_handler_id += 1;
        
        self.event_handlers.insert(handler_id, handler).map_err(|_| {
            Error::Resource(openwrt_core::error::ResourceError::Exhausted(
                "Event handler table full".to_string()
            ))
        })?;
        
        Ok(handler_id)
    }

    /// Unregister an event handler
    pub fn unregister_event_handler(&mut self, handler_id: u64) -> Result<()> {
        self.event_handlers.remove(&handler_id);
        Ok(())
    }

    /// Emit a network event
    fn emit_event(&mut self, event: NetworkEvent) -> Result<()> {
        log::debug!("Emitting network event: {:?}", event);
        
        // Collect matching handlers
        let mut matching_handlers: HeaplessVec<u64, 32> = HeaplessVec::new();
        
        for (&handler_id, handler) in &self.event_handlers {
            if handler.can_handle(&event) {
                matching_handlers.push(handler_id).map_err(|_| {
                    Error::Internal("Too many matching handlers".to_string())
                })?;
            }
        }
        
        // Sort by priority
        matching_handlers.sort_by(|&a, &b| {
            let priority_a = self.event_handlers.get(&a).map(|h| h.priority()).unwrap_or_default();
            let priority_b = self.event_handlers.get(&b).map(|h| h.priority()).unwrap_or_default();
            priority_b.cmp(&priority_a) // Higher priority first
        });
        
        // Dispatch to handlers
        for handler_id in matching_handlers {
            if let Some(handler) = self.event_handlers.get_mut(&handler_id) {
                if let Err(e) = handler.handle_event(&event) {
                    log::error!("Event handler {} failed: {}", handler_id, e);
                    self.error_count += 1;
                }
            }
        }
        
        Ok(())
    }

    /// Scan for network changes
    pub fn scan_interfaces(&mut self) -> Result<()> {
        log::debug!("Scanning for interface changes");
        
        // In a real implementation, this would scan the system for interface changes
        // and emit appropriate events
        
        Ok(())
    }

    /// Get network statistics
    pub fn get_statistics(&self) -> Result<NetworkStatistics> {
        Ok(NetworkStatistics {
            total_interfaces: self.interface_manager.interface_count() as u32,
            active_interfaces: self.interface_manager.active_interface_count() as u32,
            total_routes: self.route_manager.route_count() as u32,
            total_errors: self.error_count,
            uptime: self.start_time.map(|start| {
                // In a real implementation, get current time and calculate uptime
                0
            }).unwrap_or(0),
        })
    }
}

/// Network statistics
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct NetworkStatistics {
    /// Total number of interfaces
    pub total_interfaces: u32,
    /// Number of active interfaces
    pub active_interfaces: u32,
    /// Total number of routes
    pub total_routes: u32,
    /// Total error count
    pub total_errors: u64,
    /// System uptime in seconds
    pub uptime: u64,
}

impl Configurable for NetworkManager {
    type Config = NetworkConfig;

    fn load_config(&mut self) -> Result<Self::Config> {
        // In a real implementation, load from file system
        Ok(NetworkConfig::default())
    }

    fn save_config(&self, config: &Self::Config) -> Result<()> {
        // In a real implementation, save to file system
        log::info!("Saving network configuration");
        Ok(())
    }

    fn validate_config(&self, config: &Self::Config) -> Result<()> {
        // Validate network configuration
        for interface in &config.interfaces {
            if interface.name.is_empty() {
                return Err(Error::InvalidInput("Interface name cannot be empty".to_string()));
            }
        }
        Ok(())
    }

    fn apply_config(&mut self, config: &Self::Config) -> Result<()> {
        log::info!("Applying network configuration");
        
        // Validate first
        self.validate_config(config)?;
        
        // Apply interface configurations
        for interface_config in &config.interfaces {
            self.configure_interface(interface_config)?;
            if interface_config.enabled {
                self.start_interface(&interface_config.name)?;
            }
        }
        
        // Apply routes
        for route_config in &config.routes {
            let route = crate::Route {
                destination: route_config.destination.clone(),
                gateway: route_config.gateway.clone(),
                metric: route_config.metric,
                interface: route_config.interface.clone(),
                table: route_config.table.clone(),
            };
            self.add_route(&route)?;
        }
        
        self.network_config = Some(config.clone());
        Ok(())
    }

    fn get_config(&self) -> Result<Self::Config> {
        self.network_config.clone().ok_or_else(|| {
            Error::InvalidInput("No configuration loaded".to_string())
        })
    }
}

impl Lifecycle for NetworkManager {
    fn initialize(&mut self) -> Result<()> {
        log::info!("Initializing network manager");
        
        self.interface_manager.initialize()?;
        self.protocol_manager.initialize()?;
        self.route_manager.initialize()?;
        
        self.status = ComponentStatus::Initialized;
        Ok(())
    }

    fn start(&mut self) -> Result<()> {
        log::info!("Starting network manager");
        
        self.interface_manager.start()?;
        self.protocol_manager.start()?;
        self.route_manager.start()?;
        
        self.start_time = Some(0); // In real implementation, get current timestamp
        self.status = ComponentStatus::Running;
        Ok(())
    }

    fn stop(&mut self) -> Result<()> {
        log::info!("Stopping network manager");
        
        self.route_manager.stop()?;
        self.protocol_manager.stop()?;
        self.interface_manager.stop()?;
        
        self.status = ComponentStatus::Stopped;
        Ok(())
    }

    fn shutdown(&mut self) -> Result<()> {
        log::info!("Shutting down network manager");
        
        self.stop()?;
        
        self.route_manager.shutdown()?;
        self.protocol_manager.shutdown()?;
        self.interface_manager.shutdown()?;
        
        self.status = ComponentStatus::Uninitialized;
        Ok(())
    }

    fn is_running(&self) -> bool {
        matches!(self.status, ComponentStatus::Running)
    }

    fn status(&self) -> ComponentStatus {
        self.status.clone()
    }
}

impl Monitorable for NetworkManager {
    type HealthInfo = NetworkHealthInfo;

    fn health_check(&self) -> Result<Self::HealthInfo> {
        let stats = self.get_statistics()?;
        
        Ok(NetworkHealthInfo {
            active_interfaces: stats.active_interfaces,
            configured_routes: stats.total_routes,
            total_errors: stats.total_errors,
            last_error: None, // In real implementation, track last error time
            uptime: stats.uptime,
        })
    }

    fn metrics(&self) -> Result<openwrt_core::traits::Metrics> {
        let stats = self.get_statistics()?;
        
        let mut metrics = openwrt_core::traits::Metrics::default();
        metrics.active_connections = stats.active_interfaces;
        metrics.error_count = stats.total_errors;
        
        Ok(metrics)
    }

    fn uptime(&self) -> Result<core::time::Duration> {
        let uptime_secs = self.start_time.map(|_| 0).unwrap_or(0); // In real implementation, calculate actual uptime
        Ok(core::time::Duration::from_secs(uptime_secs))
    }
}

impl Default for NetworkManager {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use openwrt_config::schema::{InterfaceConfig, ProtocolConfig, InterfaceType};

    #[test]
    fn test_network_manager_creation() {
        let manager = NetworkManager::new();
        assert_eq!(manager.status(), ComponentStatus::Uninitialized);
        assert!(!manager.is_running());
    }

    #[test]
    fn test_network_manager_lifecycle() {
        let mut manager = NetworkManager::new();
        
        manager.initialize().unwrap();
        assert_eq!(manager.status(), ComponentStatus::Initialized);
        
        manager.start().unwrap();
        assert_eq!(manager.status(), ComponentStatus::Running);
        assert!(manager.is_running());
        
        manager.stop().unwrap();
        assert_eq!(manager.status(), ComponentStatus::Stopped);
        assert!(!manager.is_running());
        
        manager.shutdown().unwrap();
        assert_eq!(manager.status(), ComponentStatus::Uninitialized);
    }

    #[test]
    fn test_interface_configuration() {
        let mut manager = NetworkManager::new();
        manager.initialize().unwrap();
        
        let interface_config = InterfaceConfig {
            name: heapless::String::try_from("eth0").unwrap(),
            interface_type: InterfaceType::Ethernet,
            protocol: ProtocolConfig::Static {
                address: heapless::String::try_from("***********/24").unwrap(),
                netmask: heapless::String::try_from("*************").unwrap(),
                gateway: None,
                dns: heapless::Vec::new(),
            },
            device: Some(heapless::String::try_from("eth0").unwrap()),
            mac_address: None,
            mtu: Some(1500),
            enabled: true,
            metric: None,
            vlan: None,
            bridge: None,
        };
        
        manager.configure_interface(&interface_config).unwrap();
        
        let interfaces = manager.list_interfaces();
        assert!(interfaces.iter().any(|name| name == "eth0"));
    }

    #[test]
    fn test_network_statistics() {
        let manager = NetworkManager::new();
        let stats = manager.get_statistics().unwrap();
        
        assert_eq!(stats.total_interfaces, 0);
        assert_eq!(stats.active_interfaces, 0);
        assert_eq!(stats.total_routes, 0);
        assert_eq!(stats.total_errors, 0);
    }
}
