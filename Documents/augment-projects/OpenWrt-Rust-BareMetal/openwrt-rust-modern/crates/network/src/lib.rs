//! OpenWrt Network Management
//!
//! This crate provides modern network management functionality that replaces
//! the traditional netifd daemon with a more robust, type-safe, and maintainable
//! Rust implementation.
//!
//! # Features
//!
//! - **Type-Safe Configuration**: Strong typing for all network configurations
//! - **Async Operations**: Modern async/await patterns for network operations
//! - **Plugin Architecture**: Extensible protocol handlers
//! - **Event-Driven**: Real-time network state monitoring and events
//! - **Performance**: Zero-cost abstractions and efficient networking
//! - **Security**: Memory safety and secure network operations
//!
//! # Architecture
//!
//! The network management system is built around several key components:
//!
//! - **NetworkManager**: Central coordinator for all network operations
//! - **InterfaceManager**: Manages individual network interfaces
//! - **ProtocolHandlers**: Handle specific protocols (DHCP, PPPoE, etc.)
//! - **RouteManager**: Manages routing tables and rules
//! - **EventSystem**: Provides real-time network state notifications
//!
//! # Example
//!
//! ```rust
//! use openwrt_network::{NetworkManager, InterfaceConfig, ProtocolType};
//!
//! let mut manager = NetworkManager::new().await?;
//!
//! let interface_config = InterfaceConfig {
//!     name: "eth0".into(),
//!     protocol: ProtocolType::Dhcp,
//!     enabled: true,
//!     // ... other configuration
//! };
//!
//! manager.configure_interface(&interface_config).await?;
//! manager.start_interface("eth0").await?;
//! ```

#![cfg_attr(not(feature = "std"), no_std)]
#![deny(missing_docs)]

extern crate alloc;

pub mod manager;
pub mod interface;
pub mod protocol;
pub mod routing;
pub mod events;
pub mod bridge;
pub mod vlan;

#[cfg(feature = "system-integration")]
pub mod system;

// Re-export commonly used types
pub use manager::{NetworkManager, NetworkManagerConfig};
pub use interface::{InterfaceManager, InterfaceState, InterfaceStatistics};
pub use protocol::{ProtocolHandler, ProtocolManager, ProtocolType};
pub use routing::{RouteManager, Route, RoutingTable};
pub use events::{NetworkEvent, NetworkEventHandler};
pub use openwrt_core::{Error, Result};
pub use openwrt_config::schema::{InterfaceConfig, NetworkConfig};

/// Network management version
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

/// Default network configuration paths
pub mod paths {
    /// Default configuration directory
    pub const CONFIG_DIR: &str = "/etc/config";
    /// Network configuration file
    pub const NETWORK_CONFIG: &str = "/etc/config/network";
    /// Interface state directory
    pub const STATE_DIR: &str = "/var/state/network";
    /// Runtime directory
    pub const RUN_DIR: &str = "/var/run/network";
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_version() {
        assert!(!VERSION.is_empty());
    }

    #[test]
    fn test_paths() {
        assert_eq!(paths::CONFIG_DIR, "/etc/config");
        assert_eq!(paths::NETWORK_CONFIG, "/etc/config/network");
        assert_eq!(paths::STATE_DIR, "/var/state/network");
        assert_eq!(paths::RUN_DIR, "/var/run/network");
    }
}
