[package]
name = "openwrt-network"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
description = "Modern network management replacing netifd functionality"
keywords.workspace = true
categories.workspace = true

[dependencies]
# Core dependencies
openwrt-core = { path = "../core" }
openwrt-utils = { path = "../utils" }
openwrt-config = { path = "../config" }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
thiserror = { workspace = true }
log = { workspace = true }
heapless = { workspace = true }
bitflags = { workspace = true }

# Networking
smoltcp = { workspace = true }

# Async support
tokio = { workspace = true, features = ["rt", "net", "time", "macros", "process"], optional = true }
futures = { workspace = true, optional = true }

# System integration
libc = { version = "0.2", optional = true }
nix = { version = "0.27", optional = true }

[dev-dependencies]
tokio = { workspace = true, features = ["rt", "macros", "time", "test-util"] }
proptest = { workspace = true }

[features]
default = ["std", "async"]
std = ["tokio", "futures", "libc", "nix"]
async = ["tokio", "futures"]
system-integration = ["libc", "nix"]

[lib]
name = "openwrt_network"
path = "src/lib.rs"
