//! OpenWrt Compatibility Layer
//!
//! This crate provides backward compatibility with legacy OpenWrt components,
//! allowing gradual migration from the traditional C-based system to the modern
//! Rust implementation while maintaining full functionality.
//!
//! # Features
//!
//! - **UCI Compatibility**: Legacy UCI configuration system support
//! - **ubus Compatibility**: Legacy ubus IPC system support  
//! - **uloop Compatibility**: Legacy uloop event system support
//! - **C API Bridge**: FFI layer for C components
//! - **Configuration Migration**: Tools for migrating legacy configurations
//! - **Gradual Migration**: Support for hybrid C/Rust deployments
//!
//! # Architecture
//!
//! The compatibility layer acts as a bridge between legacy and modern components:
//!
//! ```text
//! Legacy C Components
//!         |
//!         v
//! Compatibility Layer (FFI)
//!         |
//!         v
//! Modern Rust Components
//! ```
//!
//! # Example
//!
//! ```rust
//! use openwrt_compat::{UciCompat, UbusCompat, UloopCompat};
//!
//! // Initialize compatibility layers
//! let uci = UciCompat::new()?;
//! let ubus = UbusCompat::new()?;
//! let uloop = UloopCompat::new()?;
//!
//! // Legacy UCI operations work transparently
//! uci.set("network.lan.proto", "static")?;
//! let proto = uci.get("network.lan.proto")?;
//! ```

#![deny(missing_docs)]

pub mod uci;
pub mod ubus;
pub mod uloop;
pub mod ffi;
pub mod migration;

// Re-export commonly used types
pub use uci::{UciCompat, UciContext, UciPackage, UciSection, UciOption};
pub use ubus::{UbusCompat, UbusContext, UbusObject, UbusMethod};
pub use uloop::{UloopCompat, UloopTimeout, UloopFd, UloopProcess};
pub use migration::{ConfigMigrator, MigrationPlan, MigrationStep};
pub use openwrt_core::{Error, Result};

/// Compatibility library version
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

/// Compatibility mode
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum CompatMode {
    /// Full compatibility (all legacy APIs available)
    Full,
    /// Partial compatibility (some legacy APIs deprecated)
    Partial,
    /// Migration mode (legacy APIs emit warnings)
    Migration,
    /// Modern only (legacy APIs disabled)
    Modern,
}

impl Default for CompatMode {
    fn default() -> Self {
        Self::Full
    }
}

/// Compatibility configuration
#[derive(Debug, Clone)]
pub struct CompatConfig {
    /// Compatibility mode
    pub mode: CompatMode,
    /// Enable UCI compatibility
    pub uci_enabled: bool,
    /// Enable ubus compatibility
    pub ubus_enabled: bool,
    /// Enable uloop compatibility
    pub uloop_enabled: bool,
    /// Emit deprecation warnings
    pub warn_deprecated: bool,
    /// Log legacy API usage
    pub log_usage: bool,
}

impl Default for CompatConfig {
    fn default() -> Self {
        Self {
            mode: CompatMode::default(),
            uci_enabled: true,
            ubus_enabled: true,
            uloop_enabled: true,
            warn_deprecated: true,
            log_usage: false,
        }
    }
}

/// Compatibility layer manager
pub struct CompatManager {
    /// Configuration
    config: CompatConfig,
    /// UCI compatibility layer
    uci: Option<UciCompat>,
    /// ubus compatibility layer
    ubus: Option<UbusCompat>,
    /// uloop compatibility layer
    uloop: Option<UloopCompat>,
    /// Migration tools
    migrator: Option<ConfigMigrator>,
}

impl CompatManager {
    /// Create a new compatibility manager
    pub fn new() -> Self {
        Self {
            config: CompatConfig::default(),
            uci: None,
            ubus: None,
            uloop: None,
            migrator: None,
        }
    }

    /// Create a compatibility manager with custom configuration
    pub fn with_config(config: CompatConfig) -> Self {
        Self {
            config,
            uci: None,
            ubus: None,
            uloop: None,
            migrator: None,
        }
    }

    /// Initialize the compatibility layer
    pub fn initialize(&mut self) -> Result<()> {
        log::info!("Initializing OpenWrt compatibility layer");
        log::info!("Compatibility mode: {:?}", self.config.mode);

        // Initialize UCI compatibility if enabled
        if self.config.uci_enabled {
            log::info!("Initializing UCI compatibility layer");
            self.uci = Some(UciCompat::new()?);
        }

        // Initialize ubus compatibility if enabled
        if self.config.ubus_enabled {
            log::info!("Initializing ubus compatibility layer");
            self.ubus = Some(UbusCompat::new()?);
        }

        // Initialize uloop compatibility if enabled
        if self.config.uloop_enabled {
            log::info!("Initializing uloop compatibility layer");
            self.uloop = Some(UloopCompat::new()?);
        }

        // Initialize migration tools
        self.migrator = Some(ConfigMigrator::new()?);

        log::info!("Compatibility layer initialized successfully");
        Ok(())
    }

    /// Shutdown the compatibility layer
    pub fn shutdown(&mut self) -> Result<()> {
        log::info!("Shutting down OpenWrt compatibility layer");

        // Shutdown in reverse order
        if let Some(mut migrator) = self.migrator.take() {
            migrator.shutdown()?;
        }

        if let Some(mut uloop) = self.uloop.take() {
            uloop.shutdown()?;
        }

        if let Some(mut ubus) = self.ubus.take() {
            ubus.shutdown()?;
        }

        if let Some(mut uci) = self.uci.take() {
            uci.shutdown()?;
        }

        log::info!("Compatibility layer shutdown complete");
        Ok(())
    }

    /// Get UCI compatibility layer
    pub fn uci(&self) -> Option<&UciCompat> {
        self.uci.as_ref()
    }

    /// Get mutable UCI compatibility layer
    pub fn uci_mut(&mut self) -> Option<&mut UciCompat> {
        self.uci.as_mut()
    }

    /// Get ubus compatibility layer
    pub fn ubus(&self) -> Option<&UbusCompat> {
        self.ubus.as_ref()
    }

    /// Get mutable ubus compatibility layer
    pub fn ubus_mut(&mut self) -> Option<&mut UbusCompat> {
        self.ubus.as_mut()
    }

    /// Get uloop compatibility layer
    pub fn uloop(&self) -> Option<&UloopCompat> {
        self.uloop.as_ref()
    }

    /// Get mutable uloop compatibility layer
    pub fn uloop_mut(&mut self) -> Option<&mut UloopCompat> {
        self.uloop.as_mut()
    }

    /// Get configuration migrator
    pub fn migrator(&self) -> Option<&ConfigMigrator> {
        self.migrator.as_ref()
    }

    /// Get mutable configuration migrator
    pub fn migrator_mut(&mut self) -> Option<&mut ConfigMigrator> {
        self.migrator.as_mut()
    }

    /// Check if a legacy API is available
    pub fn is_api_available(&self, api: &str) -> bool {
        match self.config.mode {
            CompatMode::Modern => false,
            CompatMode::Migration => {
                if self.config.warn_deprecated {
                    log::warn!("Legacy API '{}' is deprecated", api);
                }
                true
            }
            CompatMode::Partial | CompatMode::Full => true,
        }
    }

    /// Log legacy API usage
    pub fn log_api_usage(&self, api: &str, function: &str) {
        if self.config.log_usage {
            log::debug!("Legacy API usage: {}::{}", api, function);
        }
    }

    /// Migrate legacy configuration
    pub fn migrate_config(&mut self, source_path: &str, target_path: &str) -> Result<()> {
        if let Some(migrator) = &mut self.migrator {
            migrator.migrate_file(source_path, target_path)
        } else {
            Err(Error::Internal("Migration tools not initialized".to_string()))
        }
    }

    /// Get compatibility statistics
    pub fn get_statistics(&self) -> CompatStatistics {
        CompatStatistics {
            uci_calls: self.uci.as_ref().map(|u| u.get_call_count()).unwrap_or(0),
            ubus_calls: self.ubus.as_ref().map(|u| u.get_call_count()).unwrap_or(0),
            uloop_calls: self.uloop.as_ref().map(|u| u.get_call_count()).unwrap_or(0),
            migrations_performed: self.migrator.as_ref().map(|m| m.get_migration_count()).unwrap_or(0),
        }
    }
}

impl Default for CompatManager {
    fn default() -> Self {
        Self::new()
    }
}

/// Compatibility statistics
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct CompatStatistics {
    /// Number of UCI API calls
    pub uci_calls: u64,
    /// Number of ubus API calls
    pub ubus_calls: u64,
    /// Number of uloop API calls
    pub uloop_calls: u64,
    /// Number of migrations performed
    pub migrations_performed: u64,
}

/// Global compatibility manager instance
static mut COMPAT_MANAGER: Option<CompatManager> = None;
static mut COMPAT_INITIALIZED: bool = false;

/// Initialize global compatibility layer
pub fn init_compat() -> Result<()> {
    unsafe {
        if COMPAT_INITIALIZED {
            return Ok(());
        }

        let mut manager = CompatManager::new();
        manager.initialize()?;
        
        COMPAT_MANAGER = Some(manager);
        COMPAT_INITIALIZED = true;
    }
    
    Ok(())
}

/// Initialize global compatibility layer with custom configuration
pub fn init_compat_with_config(config: CompatConfig) -> Result<()> {
    unsafe {
        if COMPAT_INITIALIZED {
            return Ok(());
        }

        let mut manager = CompatManager::with_config(config);
        manager.initialize()?;
        
        COMPAT_MANAGER = Some(manager);
        COMPAT_INITIALIZED = true;
    }
    
    Ok(())
}

/// Shutdown global compatibility layer
pub fn shutdown_compat() -> Result<()> {
    unsafe {
        if !COMPAT_INITIALIZED {
            return Ok(());
        }

        if let Some(mut manager) = COMPAT_MANAGER.take() {
            manager.shutdown()?;
        }
        
        COMPAT_INITIALIZED = false;
    }
    
    Ok(())
}

/// Get global compatibility manager
pub fn get_compat_manager() -> Option<&'static CompatManager> {
    unsafe {
        COMPAT_MANAGER.as_ref()
    }
}

/// Get mutable global compatibility manager
pub fn get_compat_manager_mut() -> Option<&'static mut CompatManager> {
    unsafe {
        COMPAT_MANAGER.as_mut()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_version() {
        assert!(!VERSION.is_empty());
    }

    #[test]
    fn test_compat_modes() {
        assert_eq!(CompatMode::default(), CompatMode::Full);
        assert_ne!(CompatMode::Full, CompatMode::Modern);
    }

    #[test]
    fn test_compat_config() {
        let config = CompatConfig::default();
        assert_eq!(config.mode, CompatMode::Full);
        assert!(config.uci_enabled);
        assert!(config.ubus_enabled);
        assert!(config.uloop_enabled);
        assert!(config.warn_deprecated);
        assert!(!config.log_usage);
    }

    #[test]
    fn test_compat_manager() {
        let manager = CompatManager::new();
        assert!(manager.uci().is_none());
        assert!(manager.ubus().is_none());
        assert!(manager.uloop().is_none());
        assert!(manager.migrator().is_none());
    }

    #[test]
    fn test_api_availability() {
        let manager = CompatManager::new();
        assert!(manager.is_api_available("uci_get"));
        
        let config = CompatConfig {
            mode: CompatMode::Modern,
            ..Default::default()
        };
        let manager = CompatManager::with_config(config);
        assert!(!manager.is_api_available("uci_get"));
    }

    #[test]
    fn test_statistics() {
        let manager = CompatManager::new();
        let stats = manager.get_statistics();
        assert_eq!(stats.uci_calls, 0);
        assert_eq!(stats.ubus_calls, 0);
        assert_eq!(stats.uloop_calls, 0);
        assert_eq!(stats.migrations_performed, 0);
    }
}
