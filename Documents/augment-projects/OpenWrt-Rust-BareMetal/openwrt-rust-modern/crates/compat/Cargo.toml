[package]
name = "openwrt-compat"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
description = "Compatibility layer for legacy OpenWrt components"
keywords.workspace = true
categories.workspace = true

[dependencies]
# Core dependencies
openwrt-core = { path = "../core" }
openwrt-config = { path = "../config" }
openwrt-network = { path = "../network" }
openwrt-utils = { path = "../utils" }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
thiserror = { workspace = true }
log = { workspace = true }
heapless = { workspace = true }

# FFI support
libc = { version = "0.2" }

# Async support
tokio = { workspace = true, features = ["rt", "net", "time", "macros"], optional = true }
futures = { workspace = true, optional = true }

[dev-dependencies]
tokio = { workspace = true, features = ["rt", "macros", "time", "test-util"] }

[features]
default = ["std", "async", "uci-compat", "ubus-compat", "uloop-compat"]
std = ["tokio", "futures"]
async = ["tokio", "futures"]
uci-compat = []
ubus-compat = []
uloop-compat = []

[lib]
name = "openwrt_compat"
path = "src/lib.rs"
crate-type = ["cdylib", "rlib"]
