[package]
name = "openwrt-security"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
description = "Security framework for OpenWrt components"
keywords.workspace = true
categories.workspace = true

[dependencies]
# Core dependencies
openwrt-core = { path = "../core" }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
thiserror = { workspace = true }
log = { workspace = true }
heapless = { workspace = true }
bitflags = { workspace = true }

# Cryptography
ring = { version = "0.17" }
rand = { version = "0.8", default-features = false }
sha2 = { version = "0.10" }
aes-gcm = { version = "0.10" }
x25519-dalek = { version = "2.0" }
ed25519-dalek = { version = "2.0" }

# Time handling
time = { version = "0.3", default-features = false }

# Async support
tokio = { workspace = true, features = ["rt", "time", "macros"], optional = true }
futures = { workspace = true, optional = true }

# System integration
libc = { version = "0.2", optional = true }

[dev-dependencies]
tokio = { workspace = true, features = ["rt", "macros", "time", "test-util"] }
hex = "0.4"

[features]
default = ["std", "async"]
std = ["tokio", "futures", "libc", "rand/std", "time/std"]
async = ["tokio", "futures"]
hardware-security = ["libc"]

[lib]
name = "openwrt_security"
path = "src/lib.rs"
