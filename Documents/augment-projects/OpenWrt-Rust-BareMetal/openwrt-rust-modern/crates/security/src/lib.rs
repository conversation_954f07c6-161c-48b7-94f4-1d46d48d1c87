//! OpenWrt Security Framework
//!
//! This crate provides comprehensive security functionality for OpenWrt components,
//! including cryptography, authentication, authorization, and secure communication.
//!
//! # Features
//!
//! - **Modern Cryptography**: State-of-the-art cryptographic algorithms
//! - **Key Management**: Secure key generation, storage, and rotation
//! - **Authentication**: Multi-factor authentication and identity verification
//! - **Authorization**: Role-based access control and permissions
//! - **Secure Communication**: End-to-end encryption for IPC and network
//! - **Hardware Security**: Integration with hardware security modules
//!
//! # Architecture
//!
//! The security framework is built around several key components:
//!
//! - **CryptoProvider**: Cryptographic operations and key management
//! - **AuthManager**: Authentication and session management
//! - **AccessControl**: Authorization and permission checking
//! - **SecureChannel**: Encrypted communication channels
//! - **CertificateManager**: X.509 certificate management
//!
//! # Example
//!
//! ```rust
//! use openwrt_security::{CryptoProvider, Auth<PERSON>anager, AccessControl};
//!
//! // Initialize security components
//! let crypto = CryptoProvider::new()?;
//! let auth = AuthManager::new(&crypto)?;
//! let access = AccessControl::new()?;
//!
//! // Authenticate a user
//! let session = auth.authenticate("admin", "password").await?;
//!
//! // Check permissions
//! if access.check_permission(&session, "network.configure")? {
//!     // Perform privileged operation
//! }
//! ```

#![cfg_attr(not(feature = "std"), no_std)]
#![deny(missing_docs)]

extern crate alloc;

pub mod crypto;
pub mod auth;
pub mod access;
pub mod channel;
pub mod certificates;
pub mod keys;

#[cfg(feature = "hardware-security")]
pub mod hardware;

// Re-export commonly used types
pub use crypto::{CryptoProvider, CryptoConfig, Algorithm};
pub use auth::{AuthManager, AuthConfig, Session, Credential};
pub use access::{AccessControl, Permission, Role, Policy};
pub use channel::{SecureChannel, ChannelConfig};
pub use certificates::{CertificateManager, Certificate, CertificateRequest};
pub use keys::{KeyManager, KeyPair, SecretKey, PublicKey};
pub use openwrt_core::{Error, Result};

/// Security library version
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

/// Security configuration defaults
pub mod defaults {
    /// Default key size in bits
    pub const DEFAULT_KEY_SIZE: usize = 256;
    /// Default session timeout in seconds
    pub const SESSION_TIMEOUT: u64 = 3600;
    /// Default password hash iterations
    pub const HASH_ITERATIONS: u32 = 100_000;
    /// Default certificate validity in days
    pub const CERT_VALIDITY_DAYS: u64 = 365;
}

/// Security levels
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub enum SecurityLevel {
    /// Low security (development/testing)
    Low = 1,
    /// Medium security (standard deployment)
    Medium = 2,
    /// High security (sensitive environments)
    High = 3,
    /// Maximum security (critical infrastructure)
    Maximum = 4,
}

impl Default for SecurityLevel {
    fn default() -> Self {
        Self::Medium
    }
}

/// Security error types
#[derive(Debug, thiserror::Error)]
pub enum SecurityError {
    /// Cryptographic operation failed
    #[error("Cryptographic error: {0}")]
    Crypto(String),
    
    /// Authentication failed
    #[error("Authentication failed: {0}")]
    Authentication(String),
    
    /// Authorization denied
    #[error("Access denied: {0}")]
    Authorization(String),
    
    /// Invalid key or certificate
    #[error("Invalid key/certificate: {0}")]
    InvalidKey(String),
    
    /// Session expired or invalid
    #[error("Session error: {0}")]
    Session(String),
    
    /// Hardware security module error
    #[error("HSM error: {0}")]
    Hardware(String),
}

impl From<SecurityError> for openwrt_core::Error {
    fn from(err: SecurityError) -> Self {
        openwrt_core::Error::Security(err.to_string())
    }
}

/// Security context for operations
#[derive(Debug, Clone)]
pub struct SecurityContext {
    /// Security level
    pub level: SecurityLevel,
    /// Current session (if any)
    pub session: Option<Session>,
    /// Required permissions
    pub required_permissions: heapless::Vec<Permission, 16>,
    /// Audit logging enabled
    pub audit_enabled: bool,
}

impl SecurityContext {
    /// Create a new security context
    pub fn new(level: SecurityLevel) -> Self {
        Self {
            level,
            session: None,
            required_permissions: heapless::Vec::new(),
            audit_enabled: true,
        }
    }

    /// Create a context with session
    pub fn with_session(level: SecurityLevel, session: Session) -> Self {
        Self {
            level,
            session: Some(session),
            required_permissions: heapless::Vec::new(),
            audit_enabled: true,
        }
    }

    /// Add required permission
    pub fn require_permission(&mut self, permission: Permission) -> Result<()> {
        self.required_permissions.push(permission).map_err(|_| {
            Error::Resource(openwrt_core::error::ResourceError::Exhausted(
                "Too many required permissions".to_string()
            ))
        })
    }

    /// Check if context has valid session
    pub fn has_valid_session(&self) -> bool {
        self.session.as_ref().map_or(false, |s| s.is_valid())
    }

    /// Get session user ID
    pub fn user_id(&self) -> Option<&str> {
        self.session.as_ref().map(|s| s.user_id())
    }
}

impl Default for SecurityContext {
    fn default() -> Self {
        Self::new(SecurityLevel::default())
    }
}

/// Security audit event
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct AuditEvent {
    /// Event timestamp
    pub timestamp: u64,
    /// Event type
    pub event_type: AuditEventType,
    /// User ID (if applicable)
    pub user_id: Option<heapless::String<64>>,
    /// Source IP address (if applicable)
    pub source_ip: Option<heapless::String<64>>,
    /// Event description
    pub description: heapless::String<256>,
    /// Success/failure
    pub success: bool,
    /// Additional metadata
    pub metadata: heapless::FnvIndexMap<heapless::String<32>, heapless::String<128>, 8>,
}

/// Audit event types
#[derive(Debug, Clone, Copy, PartialEq, Eq, serde::Serialize, serde::Deserialize)]
pub enum AuditEventType {
    /// Authentication attempt
    Authentication,
    /// Authorization check
    Authorization,
    /// Key operation
    KeyOperation,
    /// Certificate operation
    CertificateOperation,
    /// Configuration change
    ConfigurationChange,
    /// System access
    SystemAccess,
    /// Network operation
    NetworkOperation,
    /// Security violation
    SecurityViolation,
}

/// Security metrics
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct SecurityMetrics {
    /// Total authentication attempts
    pub auth_attempts: u64,
    /// Successful authentications
    pub auth_successes: u64,
    /// Failed authentications
    pub auth_failures: u64,
    /// Active sessions
    pub active_sessions: u32,
    /// Security violations
    pub security_violations: u64,
    /// Key operations
    pub key_operations: u64,
    /// Certificate operations
    pub cert_operations: u64,
}

impl Default for SecurityMetrics {
    fn default() -> Self {
        Self {
            auth_attempts: 0,
            auth_successes: 0,
            auth_failures: 0,
            active_sessions: 0,
            security_violations: 0,
            key_operations: 0,
            cert_operations: 0,
        }
    }
}

/// Initialize security subsystem
pub fn init_security() -> Result<()> {
    log::info!("Initializing OpenWrt security subsystem");
    
    // Initialize random number generator
    crypto::init_rng()?;
    
    // Initialize key management
    keys::init_key_manager()?;
    
    // Initialize certificate management
    certificates::init_cert_manager()?;
    
    log::info!("Security subsystem initialized successfully");
    Ok(())
}

/// Shutdown security subsystem
pub fn shutdown_security() -> Result<()> {
    log::info!("Shutting down OpenWrt security subsystem");
    
    // Clear sensitive data from memory
    crypto::clear_sensitive_data()?;
    
    // Shutdown key management
    keys::shutdown_key_manager()?;
    
    // Shutdown certificate management
    certificates::shutdown_cert_manager()?;
    
    log::info!("Security subsystem shutdown complete");
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_version() {
        assert!(!VERSION.is_empty());
    }

    #[test]
    fn test_security_levels() {
        assert!(SecurityLevel::Low < SecurityLevel::Medium);
        assert!(SecurityLevel::Medium < SecurityLevel::High);
        assert!(SecurityLevel::High < SecurityLevel::Maximum);
    }

    #[test]
    fn test_security_context() {
        let mut context = SecurityContext::new(SecurityLevel::High);
        assert_eq!(context.level, SecurityLevel::High);
        assert!(!context.has_valid_session());
        assert!(context.user_id().is_none());
        
        let permission = Permission::new("test.permission");
        context.require_permission(permission).unwrap();
        assert_eq!(context.required_permissions.len(), 1);
    }

    #[test]
    fn test_defaults() {
        assert_eq!(defaults::DEFAULT_KEY_SIZE, 256);
        assert_eq!(defaults::SESSION_TIMEOUT, 3600);
        assert_eq!(defaults::HASH_ITERATIONS, 100_000);
        assert_eq!(defaults::CERT_VALIDITY_DAYS, 365);
    }

    #[test]
    fn test_security_metrics() {
        let metrics = SecurityMetrics::default();
        assert_eq!(metrics.auth_attempts, 0);
        assert_eq!(metrics.auth_successes, 0);
        assert_eq!(metrics.auth_failures, 0);
        assert_eq!(metrics.active_sessions, 0);
    }
}
