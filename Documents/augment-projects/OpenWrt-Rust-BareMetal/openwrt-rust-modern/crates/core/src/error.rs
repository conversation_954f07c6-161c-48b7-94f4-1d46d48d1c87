//! Error handling for OpenWrt core components
//!
//! This module provides a unified error handling system for all OpenWrt components.
//! It defines common error types and provides utilities for error propagation and handling.

use core::fmt;
use serde::{Deserialize, Serialize};

/// Result type alias for OpenWrt operations
pub type Result<T> = core::result::Result<T, Error>;

/// Main error type for OpenWrt operations
#[derive(Debug, <PERSON>lone, PartialEq, Eq, Serialize, Deserialize)]
pub enum Error {
    /// Configuration-related errors
    Configuration(ConfigurationError),
    /// Network-related errors
    Network(NetworkError),
    /// I/O errors
    Io(IoError),
    /// Resource management errors
    Resource(ResourceError),
    /// Security-related errors
    Security(SecurityError),
    /// Invalid input or parameters
    InvalidInput(String),
    /// Operation not supported
    NotSupported(String),
    /// Internal error
    Internal(String),
}

/// Configuration-related error types
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub enum ConfigurationError {
    /// Configuration file not found
    NotFound(String),
    /// Invalid configuration format
    InvalidFormat(String),
    /// Configuration validation failed
    ValidationFailed(String),
    /// Configuration is read-only
    ReadOnly,
    /// Configuration parsing error
    ParseError(String),
}

/// Network-related error types
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum NetworkError {
    /// Interface not found
    InterfaceNotFound(String),
    /// Invalid network address
    InvalidAddress(String),
    /// Network operation failed
    OperationFailed(String),
    /// Connection timeout
    Timeout,
    /// Permission denied
    PermissionDenied,
    /// Network unreachable
    NetworkUnreachable,
}

/// I/O error types
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum IoError {
    /// File not found
    FileNotFound(String),
    /// Permission denied
    PermissionDenied(String),
    /// Device busy
    DeviceBusy(String),
    /// Operation would block
    WouldBlock,
    /// Unexpected end of file
    UnexpectedEof,
}

/// Resource management error types
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ResourceError {
    /// Resource not available
    NotAvailable(String),
    /// Resource exhausted
    Exhausted(String),
    /// Resource locked
    Locked(String),
    /// Invalid resource state
    InvalidState(String),
}

/// Security-related error types
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum SecurityError {
    /// Authentication failed
    AuthenticationFailed,
    /// Authorization denied
    AuthorizationDenied,
    /// Invalid credentials
    InvalidCredentials,
    /// Security policy violation
    PolicyViolation(String),
    /// Cryptographic operation failed
    CryptographicError(String),
}

impl fmt::Display for Error {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Error::Configuration(e) => write!(f, "Configuration error: {}", e),
            Error::Network(e) => write!(f, "Network error: {}", e),
            Error::Io(e) => write!(f, "I/O error: {}", e),
            Error::Resource(e) => write!(f, "Resource error: {}", e),
            Error::Security(e) => write!(f, "Security error: {}", e),
            Error::InvalidInput(msg) => write!(f, "Invalid input: {}", msg),
            Error::NotSupported(msg) => write!(f, "Not supported: {}", msg),
            Error::Internal(msg) => write!(f, "Internal error: {}", msg),
        }
    }
}

impl fmt::Display for ConfigurationError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ConfigurationError::NotFound(path) => write!(f, "Configuration not found: {}", path),
            ConfigurationError::InvalidFormat(msg) => write!(f, "Invalid format: {}", msg),
            ConfigurationError::ValidationFailed(msg) => write!(f, "Validation failed: {}", msg),
            ConfigurationError::ReadOnly => write!(f, "Configuration is read-only"),
            ConfigurationError::ParseError(msg) => write!(f, "Parse error: {}", msg),
        }
    }
}

impl fmt::Display for NetworkError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            NetworkError::InterfaceNotFound(name) => write!(f, "Interface not found: {}", name),
            NetworkError::InvalidAddress(addr) => write!(f, "Invalid address: {}", addr),
            NetworkError::OperationFailed(msg) => write!(f, "Operation failed: {}", msg),
            NetworkError::Timeout => write!(f, "Connection timeout"),
            NetworkError::PermissionDenied => write!(f, "Permission denied"),
            NetworkError::NetworkUnreachable => write!(f, "Network unreachable"),
        }
    }
}

impl fmt::Display for IoError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            IoError::FileNotFound(path) => write!(f, "File not found: {}", path),
            IoError::PermissionDenied(msg) => write!(f, "Permission denied: {}", msg),
            IoError::DeviceBusy(device) => write!(f, "Device busy: {}", device),
            IoError::WouldBlock => write!(f, "Operation would block"),
            IoError::UnexpectedEof => write!(f, "Unexpected end of file"),
        }
    }
}

impl fmt::Display for ResourceError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ResourceError::NotAvailable(resource) => write!(f, "Resource not available: {}", resource),
            ResourceError::Exhausted(resource) => write!(f, "Resource exhausted: {}", resource),
            ResourceError::Locked(resource) => write!(f, "Resource locked: {}", resource),
            ResourceError::InvalidState(msg) => write!(f, "Invalid state: {}", msg),
        }
    }
}

impl fmt::Display for SecurityError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            SecurityError::AuthenticationFailed => write!(f, "Authentication failed"),
            SecurityError::AuthorizationDenied => write!(f, "Authorization denied"),
            SecurityError::InvalidCredentials => write!(f, "Invalid credentials"),
            SecurityError::PolicyViolation(msg) => write!(f, "Policy violation: {}", msg),
            SecurityError::CryptographicError(msg) => write!(f, "Cryptographic error: {}", msg),
        }
    }
}

#[cfg(feature = "std")]
impl std::error::Error for Error {}

#[cfg(feature = "std")]
impl std::error::Error for ConfigurationError {}

#[cfg(feature = "std")]
impl std::error::Error for NetworkError {}

#[cfg(feature = "std")]
impl std::error::Error for IoError {}

#[cfg(feature = "std")]
impl std::error::Error for ResourceError {}

#[cfg(feature = "std")]
impl std::error::Error for SecurityError {}

/// Convenience macros for error creation
#[macro_export]
macro_rules! config_error {
    ($variant:ident, $msg:expr) => {
        $crate::Error::Configuration($crate::error::ConfigurationError::$variant($msg.to_string()))
    };
    ($variant:ident) => {
        $crate::Error::Configuration($crate::error::ConfigurationError::$variant)
    };
}

#[macro_export]
macro_rules! network_error {
    ($variant:ident, $msg:expr) => {
        $crate::Error::Network($crate::error::NetworkError::$variant($msg.to_string()))
    };
    ($variant:ident) => {
        $crate::Error::Network($crate::error::NetworkError::$variant)
    };
}

#[macro_export]
macro_rules! io_error {
    ($variant:ident, $msg:expr) => {
        $crate::Error::Io($crate::error::IoError::$variant($msg.to_string()))
    };
    ($variant:ident) => {
        $crate::Error::Io($crate::error::IoError::$variant)
    };
}

#[macro_export]
macro_rules! resource_error {
    ($variant:ident, $msg:expr) => {
        $crate::Error::Resource($crate::error::ResourceError::$variant($msg.to_string()))
    };
    ($variant:ident) => {
        $crate::Error::Resource($crate::error::ResourceError::$variant)
    };
}

#[macro_export]
macro_rules! security_error {
    ($variant:ident, $msg:expr) => {
        $crate::Error::Security($crate::error::SecurityError::$variant($msg.to_string()))
    };
    ($variant:ident) => {
        $crate::Error::Security($crate::error::SecurityError::$variant)
    };
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_display() {
        let error = Error::InvalidInput("test".to_string());
        assert_eq!(format!("{}", error), "Invalid input: test");
    }

    #[test]
    fn test_error_macros() {
        let error = config_error!(NotFound, "test.conf");
        match error {
            Error::Configuration(ConfigurationError::NotFound(path)) => {
                assert_eq!(path, "test.conf");
            }
            _ => panic!("Unexpected error type"),
        }
    }

    #[test]
    fn test_error_serialization() {
        let error = Error::InvalidInput("test".to_string());
        let serialized = serde_json::to_string(&error).unwrap();
        let deserialized: Error = serde_json::from_str(&serialized).unwrap();
        assert_eq!(error, deserialized);
    }
}
