//! Core traits for OpenWrt components
//!
//! This module defines the fundamental traits that provide abstractions for
//! various OpenWrt components. These traits enable modularity, testability,
//! and composability throughout the system.

use crate::{Error, Result};
use serde::{Deserialize, Serialize};

/// Trait for components that can be configured
pub trait Configurable {
    /// Configuration type for this component
    type Config: Serialize + for<'de> Deserialize<'de> + Clone + Send + Sync;

    /// Load configuration from the default source
    fn load_config(&mut self) -> Result<Self::Config>;

    /// Save configuration to the default destination
    fn save_config(&self, config: &Self::Config) -> Result<()>;

    /// Validate configuration without applying it
    fn validate_config(&self, config: &Self::Config) -> Result<()>;

    /// Apply configuration to the component
    fn apply_config(&mut self, config: &Self::Config) -> Result<()>;

    /// Get current configuration
    fn get_config(&self) -> Result<Self::Config>;
}

/// Trait for components that have a lifecycle
pub trait Lifecycle {
    /// Initialize the component
    fn initialize(&mut self) -> Result<()>;

    /// Start the component
    fn start(&mut self) -> Result<()>;

    /// Stop the component
    fn stop(&mut self) -> Result<()>;

    /// Shutdown the component and cleanup resources
    fn shutdown(&mut self) -> Result<()>;

    /// Check if the component is running
    fn is_running(&self) -> bool;

    /// Get component status
    fn status(&self) -> ComponentStatus;
}

/// Component status information
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ComponentStatus {
    /// Component is not initialized
    Uninitialized,
    /// Component is initialized but not started
    Initialized,
    /// Component is running normally
    Running,
    /// Component is stopping
    Stopping,
    /// Component has stopped
    Stopped,
    /// Component is in error state
    Error(String),
}

/// Trait for components that can be monitored
pub trait Monitorable {
    /// Health check result
    type HealthInfo: Serialize + for<'de> Deserialize<'de> + Clone + Send + Sync;

    /// Perform health check
    fn health_check(&self) -> Result<Self::HealthInfo>;

    /// Get performance metrics
    fn metrics(&self) -> Result<Metrics>;

    /// Get component uptime
    fn uptime(&self) -> Result<core::time::Duration>;
}

/// Performance metrics
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct Metrics {
    /// CPU usage percentage (0.0 to 100.0)
    pub cpu_usage: f64,
    /// Memory usage in bytes
    pub memory_usage: u64,
    /// Network bytes received
    pub network_rx_bytes: u64,
    /// Network bytes transmitted
    pub network_tx_bytes: u64,
    /// Number of active connections
    pub active_connections: u32,
    /// Error count
    pub error_count: u64,
    /// Custom metrics
    pub custom: heapless::FnvIndexMap<heapless::String<32>, f64, 16>,
}

impl Default for Metrics {
    fn default() -> Self {
        Self {
            cpu_usage: 0.0,
            memory_usage: 0,
            network_rx_bytes: 0,
            network_tx_bytes: 0,
            active_connections: 0,
            error_count: 0,
            custom: heapless::FnvIndexMap::new(),
        }
    }
}

/// Trait for components that can handle events
pub trait EventHandler<E> {
    /// Handle an event
    fn handle_event(&mut self, event: E) -> Result<()>;

    /// Check if this handler can process the given event type
    fn can_handle(&self, event: &E) -> bool;

    /// Get handler priority (higher values = higher priority)
    fn priority(&self) -> u8 {
        0
    }
}

/// Trait for components that can be validated
pub trait Validatable {
    /// Validation result type
    type ValidationResult: Serialize + for<'de> Deserialize<'de> + Clone + Send + Sync;

    /// Validate the component's current state
    fn validate(&self) -> Result<Self::ValidationResult>;

    /// Perform deep validation (may be expensive)
    fn deep_validate(&self) -> Result<Self::ValidationResult> {
        self.validate()
    }
}

/// Trait for components that support hot-reload
pub trait HotReloadable {
    /// Reload the component without stopping it
    fn reload(&mut self) -> Result<()>;

    /// Check if hot reload is supported in current state
    fn can_reload(&self) -> bool;

    /// Get reload status
    fn reload_status(&self) -> ReloadStatus;
}

/// Hot reload status
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ReloadStatus {
    /// Ready for reload
    Ready,
    /// Reload in progress
    InProgress,
    /// Reload completed successfully
    Completed,
    /// Reload failed
    Failed(String),
    /// Reload not supported
    NotSupported,
}

/// Trait for components that can be backed up and restored
pub trait Backupable {
    /// Backup data type
    type BackupData: Serialize + for<'de> Deserialize<'de> + Clone + Send + Sync;

    /// Create a backup of the component's state
    fn backup(&self) -> Result<Self::BackupData>;

    /// Restore component state from backup
    fn restore(&mut self, backup: &Self::BackupData) -> Result<()>;

    /// Validate backup data
    fn validate_backup(&self, backup: &Self::BackupData) -> Result<()>;
}

/// Trait for components that support versioning
pub trait Versioned {
    /// Get component version
    fn version(&self) -> &str;

    /// Get API version
    fn api_version(&self) -> &str;

    /// Check compatibility with another version
    fn is_compatible_with(&self, other_version: &str) -> bool;
}

/// Trait for components that can be debugged
pub trait Debuggable {
    /// Debug information type
    type DebugInfo: Serialize + for<'de> Deserialize<'de> + Clone + Send + Sync;

    /// Get debug information
    fn debug_info(&self) -> Result<Self::DebugInfo>;

    /// Enable debug mode
    fn enable_debug(&mut self) -> Result<()>;

    /// Disable debug mode
    fn disable_debug(&mut self) -> Result<()>;

    /// Check if debug mode is enabled
    fn is_debug_enabled(&self) -> bool;
}

/// Trait for async components
#[cfg(feature = "async")]
pub trait AsyncLifecycle {
    /// Async initialization
    async fn initialize_async(&mut self) -> Result<()>;

    /// Async start
    async fn start_async(&mut self) -> Result<()>;

    /// Async stop
    async fn stop_async(&mut self) -> Result<()>;

    /// Async shutdown
    async fn shutdown_async(&mut self) -> Result<()>;
}

/// Trait for async event handling
#[cfg(feature = "async")]
pub trait AsyncEventHandler<E> {
    /// Handle an event asynchronously
    async fn handle_event_async(&mut self, event: E) -> Result<()>;
}

#[cfg(test)]
mod tests {
    use super::*;

    #[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
    struct TestConfig {
        name: String,
        value: u32,
    }

    struct TestComponent {
        config: Option<TestConfig>,
        status: ComponentStatus,
    }

    impl TestComponent {
        fn new() -> Self {
            Self {
                config: None,
                status: ComponentStatus::Uninitialized,
            }
        }
    }

    impl Configurable for TestComponent {
        type Config = TestConfig;

        fn load_config(&mut self) -> Result<Self::Config> {
            Ok(TestConfig {
                name: "test".to_string(),
                value: 42,
            })
        }

        fn save_config(&self, _config: &Self::Config) -> Result<()> {
            Ok(())
        }

        fn validate_config(&self, _config: &Self::Config) -> Result<()> {
            Ok(())
        }

        fn apply_config(&mut self, config: &Self::Config) -> Result<()> {
            self.config = Some(config.clone());
            Ok(())
        }

        fn get_config(&self) -> Result<Self::Config> {
            self.config.clone().ok_or_else(|| Error::InvalidInput("No config set".to_string()))
        }
    }

    impl Lifecycle for TestComponent {
        fn initialize(&mut self) -> Result<()> {
            self.status = ComponentStatus::Initialized;
            Ok(())
        }

        fn start(&mut self) -> Result<()> {
            self.status = ComponentStatus::Running;
            Ok(())
        }

        fn stop(&mut self) -> Result<()> {
            self.status = ComponentStatus::Stopped;
            Ok(())
        }

        fn shutdown(&mut self) -> Result<()> {
            self.status = ComponentStatus::Uninitialized;
            Ok(())
        }

        fn is_running(&self) -> bool {
            matches!(self.status, ComponentStatus::Running)
        }

        fn status(&self) -> ComponentStatus {
            self.status.clone()
        }
    }

    #[test]
    fn test_configurable_trait() {
        let mut component = TestComponent::new();
        let config = component.load_config().unwrap();
        assert_eq!(config.name, "test");
        assert_eq!(config.value, 42);

        component.apply_config(&config).unwrap();
        let retrieved_config = component.get_config().unwrap();
        assert_eq!(retrieved_config, config);
    }

    #[test]
    fn test_lifecycle_trait() {
        let mut component = TestComponent::new();
        assert_eq!(component.status(), ComponentStatus::Uninitialized);
        assert!(!component.is_running());

        component.initialize().unwrap();
        assert_eq!(component.status(), ComponentStatus::Initialized);

        component.start().unwrap();
        assert_eq!(component.status(), ComponentStatus::Running);
        assert!(component.is_running());

        component.stop().unwrap();
        assert_eq!(component.status(), ComponentStatus::Stopped);
        assert!(!component.is_running());
    }

    #[test]
    fn test_metrics_default() {
        let metrics = Metrics::default();
        assert_eq!(metrics.cpu_usage, 0.0);
        assert_eq!(metrics.memory_usage, 0);
        assert_eq!(metrics.error_count, 0);
    }
}
