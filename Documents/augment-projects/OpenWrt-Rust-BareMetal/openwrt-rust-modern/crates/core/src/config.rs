//! Configuration management abstractions

use crate::{<PERSON>rro<PERSON>, Result};
use serde::{Deserialize, Serialize};

/// Configuration provider trait
pub trait ConfigProvider {
    /// Configuration type
    type Config: Serialize + for<'de> Deserialize<'de> + <PERSON>lone + Send + Sync;

    /// Load configuration
    fn load(&self) -> Result<Self::Config>;

    /// Save configuration
    fn save(&self, config: &Self::Config) -> Result<()>;

    /// Validate configuration
    fn validate(&self, config: &Self::Config) -> Result<()>;
}

/// Configuration change notification
#[derive(Debug, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub struct ConfigChange {
    /// Path to changed configuration
    pub path: heapless::String<256>,
    /// Type of change
    pub change_type: ChangeType,
    /// Timestamp of change
    pub timestamp: u64,
}

/// Type of configuration change
#[derive(Debug, <PERSON><PERSON>, <PERSON>ialEq, Eq, Serialize, Deserialize)]
pub enum ChangeType {
    /// Configuration created
    Created,
    /// Configuration modified
    Modified,
    /// Configuration deleted
    Deleted,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_config_change_serialization() {
        let change = ConfigChange {
            path: heapless::String::try_from("/etc/config/network").unwrap(),
            change_type: ChangeType::Modified,
            timestamp: 1234567890,
        };

        let serialized = serde_json::to_string(&change).unwrap();
        let deserialized: ConfigChange = serde_json::from_str(&serialized).unwrap();
        assert_eq!(change, deserialized);
    }
}
