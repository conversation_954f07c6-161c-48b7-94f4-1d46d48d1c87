//! Network interface abstractions

use crate::{<PERSON>rro<PERSON>, Result, types::*};
use serde::{Deserialize, Serialize};

/// Network interface trait
pub trait NetworkInterface {
    /// Configure the interface
    fn configure(&mut self, config: &InterfaceConfig) -> Result<()>;

    /// Start the interface
    fn start(&mut self) -> Result<()>;

    /// Stop the interface
    fn stop(&mut self) -> Result<()>;

    /// Get interface status
    fn status(&self) -> InterfaceStatus;

    /// Get interface statistics
    fn statistics(&self) -> Result<InterfaceStatistics>;
}

/// Interface configuration
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct InterfaceConfig {
    /// Interface name
    pub name: heapless::String<32>,
    /// Interface type
    pub interface_type: InterfaceType,
    /// Protocol configuration
    pub protocol: ProtocolType,
    /// MAC address
    pub mac_address: Option<MacAddress>,
    /// MTU size
    pub mtu: Option<u16>,
    /// Whether interface is enabled
    pub enabled: bool,
}

/// Interface status
#[derive(Debug, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub struct InterfaceStatus {
    /// Interface name
    pub name: heapless::String<32>,
    /// Current state
    pub state: InterfaceState,
    /// Link status
    pub link_up: bool,
    /// IP addresses
    pub ip_addresses: heapless::Vec<heapless::String<64>, 8>,
    /// MAC address
    pub mac_address: Option<MacAddress>,
}

/// Interface state
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum InterfaceState {
    /// Interface is down
    Down,
    /// Interface is up
    Up,
    /// Interface is configuring
    Configuring,
    /// Interface is in error state
    Error(heapless::String<128>),
}

/// Interface statistics
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct InterfaceStatistics {
    /// Bytes received
    pub rx_bytes: u64,
    /// Packets received
    pub rx_packets: u64,
    /// Receive errors
    pub rx_errors: u64,
    /// Bytes transmitted
    pub tx_bytes: u64,
    /// Packets transmitted
    pub tx_packets: u64,
    /// Transmit errors
    pub tx_errors: u64,
}

/// Network manager trait
pub trait NetworkManager {
    /// List all interfaces
    fn list_interfaces(&self) -> Result<heapless::Vec<heapless::String<32>, 32>>;

    /// Get interface by name
    fn get_interface(&self, name: &str) -> Result<Box<dyn NetworkInterface>>;

    /// Create new interface
    fn create_interface(&mut self, config: &InterfaceConfig) -> Result<()>;

    /// Remove interface
    fn remove_interface(&mut self, name: &str) -> Result<()>;
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_interface_config() {
        let config = InterfaceConfig {
            name: heapless::String::try_from("eth0").unwrap(),
            interface_type: InterfaceType::Ethernet,
            protocol: ProtocolType::Static,
            mac_address: Some(MacAddress::new([0x00, 0x11, 0x22, 0x33, 0x44, 0x55])),
            mtu: Some(1500),
            enabled: true,
        };

        let serialized = serde_json::to_string(&config).unwrap();
        let deserialized: InterfaceConfig = serde_json::from_str(&serialized).unwrap();
        assert_eq!(config, deserialized);
    }
}
