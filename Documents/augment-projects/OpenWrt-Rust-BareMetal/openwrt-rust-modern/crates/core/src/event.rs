//! Event system for OpenWrt components
//!
//! This module provides a modern, type-safe event system that replaces the traditional
//! uloop-based event handling with async/await patterns and strong typing.

use crate::{Error, Result, ComponentId, Priority};
use serde::{Deserialize, Serialize};
use heapless::{Vec as HeaplessVec, FnvIndexMap};

/// Event types that can be handled by the system
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum Event {
    /// Network interface events
    Network(NetworkEvent),
    /// Configuration change events
    Configuration(ConfigurationEvent),
    /// System events
    System(SystemEvent),
    /// Timer events
    Timer(TimerEvent),
    /// Custom events
    Custom(CustomEvent),
}

/// Network-related events
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum NetworkEvent {
    /// Interface state changed
    InterfaceStateChanged {
        interface: heapless::String<32>,
        old_state: InterfaceState,
        new_state: InterfaceState,
    },
    /// Interface added
    InterfaceAdded {
        interface: heapless::String<32>,
        interface_type: crate::types::InterfaceType,
    },
    /// Interface removed
    InterfaceRemoved {
        interface: heapless::String<32>,
    },
    /// Link state changed
    LinkStateChanged {
        interface: heapless::String<32>,
        link_up: bool,
    },
    /// IP address changed
    IpAddressChanged {
        interface: heapless::String<32>,
        old_address: Option<heapless::String<64>>,
        new_address: Option<heapless::String<64>>,
    },
}

/// Interface state
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum InterfaceState {
    /// Interface is down
    Down,
    /// Interface is up
    Up,
    /// Interface is configuring
    Configuring,
    /// Interface is in error state
    Error(heapless::String<128>),
}

/// Configuration-related events
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ConfigurationEvent {
    /// Configuration file changed
    FileChanged {
        path: heapless::String<256>,
        checksum: heapless::String<64>,
    },
    /// Configuration validated
    Validated {
        component: ComponentId,
        success: bool,
        errors: HeaplessVec<heapless::String<128>, 8>,
    },
    /// Configuration applied
    Applied {
        component: ComponentId,
        success: bool,
    },
    /// Configuration backup created
    BackupCreated {
        path: heapless::String<256>,
        timestamp: u64,
    },
}

/// System-related events
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum SystemEvent {
    /// System startup
    Startup,
    /// System shutdown
    Shutdown,
    /// Component started
    ComponentStarted {
        component: ComponentId,
    },
    /// Component stopped
    ComponentStopped {
        component: ComponentId,
        reason: heapless::String<128>,
    },
    /// Resource threshold exceeded
    ResourceThreshold {
        resource: heapless::String<32>,
        current: u64,
        threshold: u64,
    },
    /// Security event
    Security {
        event_type: heapless::String<64>,
        severity: SecuritySeverity,
        details: heapless::String<256>,
    },
}

/// Security event severity
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum SecuritySeverity {
    /// Informational
    Info,
    /// Warning
    Warning,
    /// Critical
    Critical,
}

/// Timer-related events
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum TimerEvent {
    /// One-shot timer expired
    Timeout {
        timer_id: u64,
    },
    /// Periodic timer tick
    Periodic {
        timer_id: u64,
        tick_count: u64,
    },
}

/// Custom events for application-specific use
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct CustomEvent {
    /// Event type identifier
    pub event_type: heapless::String<64>,
    /// Event data as JSON string
    pub data: heapless::String<512>,
    /// Event priority
    pub priority: Priority,
    /// Source component
    pub source: Option<ComponentId>,
}

/// Event handler trait
pub trait EventHandler<E> {
    /// Handle an event
    fn handle_event(&mut self, event: E) -> Result<()>;

    /// Check if this handler can process the given event
    fn can_handle(&self, event: &E) -> bool;

    /// Get handler priority
    fn priority(&self) -> Priority {
        Priority::Normal
    }
}

/// Event subscription
#[derive(Debug, Clone)]
pub struct EventSubscription {
    /// Subscription ID
    pub id: u64,
    /// Component that subscribed
    pub component: ComponentId,
    /// Event filter
    pub filter: EventFilter,
    /// Priority
    pub priority: Priority,
}

/// Event filter for subscriptions
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum EventFilter {
    /// Accept all events
    All,
    /// Accept only network events
    Network,
    /// Accept only configuration events
    Configuration,
    /// Accept only system events
    System,
    /// Accept only timer events
    Timer,
    /// Accept only custom events
    Custom,
    /// Accept events matching specific criteria
    Specific(SpecificFilter),
}

/// Specific event filter criteria
#[derive(Debug, Clone, PartialEq, Eq)]
pub struct SpecificFilter {
    /// Event type pattern
    pub event_type: Option<heapless::String<64>>,
    /// Source component filter
    pub source: Option<ComponentId>,
    /// Minimum priority
    pub min_priority: Priority,
}

/// Event loop for handling events
pub struct EventLoop {
    /// Event queue
    queue: HeaplessVec<Event, 256>,
    /// Event subscriptions
    subscriptions: FnvIndexMap<u64, EventSubscription, 64>,
    /// Next subscription ID
    next_subscription_id: u64,
    /// Running state
    running: bool,
}

impl EventLoop {
    /// Create a new event loop
    pub fn new() -> Self {
        Self {
            queue: HeaplessVec::new(),
            subscriptions: FnvIndexMap::new(),
            next_subscription_id: 1,
            running: false,
        }
    }

    /// Start the event loop
    pub fn start(&mut self) -> Result<()> {
        self.running = true;
        Ok(())
    }

    /// Stop the event loop
    pub fn stop(&mut self) -> Result<()> {
        self.running = false;
        Ok(())
    }

    /// Check if the event loop is running
    pub fn is_running(&self) -> bool {
        self.running
    }

    /// Post an event to the queue
    pub fn post_event(&mut self, event: Event) -> Result<()> {
        self.queue.push(event).map_err(|_| {
            Error::Resource(crate::error::ResourceError::Exhausted("Event queue full".to_string()))
        })
    }

    /// Subscribe to events
    pub fn subscribe(&mut self, component: ComponentId, filter: EventFilter, priority: Priority) -> Result<u64> {
        let subscription_id = self.next_subscription_id;
        self.next_subscription_id += 1;

        let subscription = EventSubscription {
            id: subscription_id,
            component,
            filter,
            priority,
        };

        self.subscriptions.insert(subscription_id, subscription).map_err(|_| {
            Error::Resource(crate::error::ResourceError::Exhausted("Subscription table full".to_string()))
        })?;

        Ok(subscription_id)
    }

    /// Unsubscribe from events
    pub fn unsubscribe(&mut self, subscription_id: u64) -> Result<()> {
        self.subscriptions.remove(&subscription_id);
        Ok(())
    }

    /// Process one event from the queue
    pub fn process_one(&mut self) -> Result<bool> {
        if let Some(event) = self.queue.pop() {
            self.dispatch_event(event)?;
            Ok(true)
        } else {
            Ok(false)
        }
    }

    /// Process all events in the queue
    pub fn process_all(&mut self) -> Result<usize> {
        let mut processed = 0;
        while self.process_one()? {
            processed += 1;
        }
        Ok(processed)
    }

    /// Dispatch an event to subscribers
    fn dispatch_event(&self, event: Event) -> Result<()> {
        // Find matching subscriptions
        let mut matching_subscriptions: HeaplessVec<&EventSubscription, 32> = HeaplessVec::new();

        for subscription in self.subscriptions.values() {
            if self.event_matches_filter(&event, &subscription.filter) {
                matching_subscriptions.push(subscription).map_err(|_| {
                    Error::Internal("Too many matching subscriptions".to_string())
                })?;
            }
        }

        // Sort by priority (highest first)
        matching_subscriptions.sort_by(|a, b| b.priority.cmp(&a.priority));

        // Dispatch to handlers (in a real implementation, this would call the actual handlers)
        for subscription in matching_subscriptions {
            log::debug!("Dispatching event to component: {}", subscription.component);
            // In a real implementation, we would call the handler here
        }

        Ok(())
    }

    /// Check if an event matches a filter
    fn event_matches_filter(&self, event: &Event, filter: &EventFilter) -> bool {
        match filter {
            EventFilter::All => true,
            EventFilter::Network => matches!(event, Event::Network(_)),
            EventFilter::Configuration => matches!(event, Event::Configuration(_)),
            EventFilter::System => matches!(event, Event::System(_)),
            EventFilter::Timer => matches!(event, Event::Timer(_)),
            EventFilter::Custom => matches!(event, Event::Custom(_)),
            EventFilter::Specific(specific) => {
                // Implement specific filtering logic
                self.event_matches_specific(event, specific)
            }
        }
    }

    /// Check if an event matches specific filter criteria
    fn event_matches_specific(&self, _event: &Event, _filter: &SpecificFilter) -> bool {
        // Implement specific matching logic
        true
    }
}

impl Default for EventLoop {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_event_loop_creation() {
        let event_loop = EventLoop::new();
        assert!(!event_loop.is_running());
    }

    #[test]
    fn test_event_loop_start_stop() {
        let mut event_loop = EventLoop::new();
        
        event_loop.start().unwrap();
        assert!(event_loop.is_running());
        
        event_loop.stop().unwrap();
        assert!(!event_loop.is_running());
    }

    #[test]
    fn test_event_posting() {
        let mut event_loop = EventLoop::new();
        
        let event = Event::System(SystemEvent::Startup);
        event_loop.post_event(event).unwrap();
        
        let processed = event_loop.process_all().unwrap();
        assert_eq!(processed, 1);
    }

    #[test]
    fn test_event_subscription() {
        let mut event_loop = EventLoop::new();
        let component_id = ComponentId::new("test-component").unwrap();
        
        let subscription_id = event_loop.subscribe(
            component_id,
            EventFilter::System,
            Priority::Normal
        ).unwrap();
        
        assert!(subscription_id > 0);
        
        event_loop.unsubscribe(subscription_id).unwrap();
    }

    #[test]
    fn test_network_event() {
        let event = Event::Network(NetworkEvent::InterfaceStateChanged {
            interface: heapless::String::try_from("eth0").unwrap(),
            old_state: InterfaceState::Down,
            new_state: InterfaceState::Up,
        });
        
        // Test serialization
        let serialized = serde_json::to_string(&event).unwrap();
        let deserialized: Event = serde_json::from_str(&serialized).unwrap();
        assert_eq!(event, deserialized);
    }
}
