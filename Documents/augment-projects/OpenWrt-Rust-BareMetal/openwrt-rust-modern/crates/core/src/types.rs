//! Common types used throughout OpenWrt components

use serde::{Deserialize, Serialize};
use core::fmt;

/// MAC address representation
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct MacAddress([u8; 6]);

impl MacAddress {
    /// Create a new MAC address from bytes
    pub const fn new(bytes: [u8; 6]) -> Self {
        Self(bytes)
    }

    /// Get MAC address as bytes
    pub const fn as_bytes(&self) -> &[u8; 6] {
        &self.0
    }

    /// Check if this is a broadcast MAC address
    pub fn is_broadcast(&self) -> bool {
        self.0 == [0xff; 6]
    }

    /// Check if this is a multicast MAC address
    pub fn is_multicast(&self) -> bool {
        self.0[0] & 0x01 != 0
    }

    /// Check if this is a unicast MAC address
    pub fn is_unicast(&self) -> bool {
        !self.is_multicast()
    }
}

impl fmt::Display for MacAddress {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{:02x}:{:02x}:{:02x}:{:02x}:{:02x}:{:02x}",
               self.0[0], self.0[1], self.0[2], self.0[3], self.0[4], self.0[5])
    }
}

impl From<[u8; 6]> for MacAddress {
    fn from(bytes: [u8; 6]) -> Self {
        Self::new(bytes)
    }
}

/// Network interface type
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum InterfaceType {
    /// Ethernet interface
    Ethernet,
    /// Wireless interface
    Wireless,
    /// Bridge interface
    Bridge,
    /// VLAN interface
    Vlan,
    /// Tunnel interface
    Tunnel,
    /// Loopback interface
    Loopback,
    /// Virtual interface
    Virtual,
    /// Unknown interface type
    Unknown,
}

/// Network protocol type
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ProtocolType {
    /// Static IP configuration
    Static,
    /// DHCP client
    Dhcp,
    /// PPPoE
    Pppoe,
    /// PPP
    Ppp,
    /// None (no protocol)
    None,
}

/// IP address version
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum IpVersion {
    /// IPv4
    V4,
    /// IPv6
    V6,
}

/// Priority level for various operations
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub enum Priority {
    /// Lowest priority
    Lowest = 0,
    /// Low priority
    Low = 1,
    /// Normal priority
    Normal = 2,
    /// High priority
    High = 3,
    /// Highest priority
    Highest = 4,
}

impl Default for Priority {
    fn default() -> Self {
        Priority::Normal
    }
}

/// Time duration in milliseconds
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub struct Duration(pub u64);

impl Duration {
    /// Create a new duration from milliseconds
    pub const fn from_millis(millis: u64) -> Self {
        Self(millis)
    }

    /// Create a new duration from seconds
    pub const fn from_secs(secs: u64) -> Self {
        Self(secs * 1000)
    }

    /// Get duration in milliseconds
    pub const fn as_millis(&self) -> u64 {
        self.0
    }

    /// Get duration in seconds
    pub const fn as_secs(&self) -> u64 {
        self.0 / 1000
    }
}

impl From<u64> for Duration {
    fn from(millis: u64) -> Self {
        Self::from_millis(millis)
    }
}

/// Size in bytes
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub struct Size(pub u64);

impl Size {
    /// Create a new size from bytes
    pub const fn from_bytes(bytes: u64) -> Self {
        Self(bytes)
    }

    /// Create a new size from kilobytes
    pub const fn from_kb(kb: u64) -> Self {
        Self(kb * 1024)
    }

    /// Create a new size from megabytes
    pub const fn from_mb(mb: u64) -> Self {
        Self(mb * 1024 * 1024)
    }

    /// Get size in bytes
    pub const fn as_bytes(&self) -> u64 {
        self.0
    }

    /// Get size in kilobytes
    pub const fn as_kb(&self) -> u64 {
        self.0 / 1024
    }

    /// Get size in megabytes
    pub const fn as_mb(&self) -> u64 {
        self.0 / (1024 * 1024)
    }
}

impl From<u64> for Size {
    fn from(bytes: u64) -> Self {
        Self::from_bytes(bytes)
    }
}

impl fmt::Display for Size {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        if self.0 >= 1024 * 1024 * 1024 {
            write!(f, "{:.1} GB", self.0 as f64 / (1024.0 * 1024.0 * 1024.0))
        } else if self.0 >= 1024 * 1024 {
            write!(f, "{:.1} MB", self.0 as f64 / (1024.0 * 1024.0))
        } else if self.0 >= 1024 {
            write!(f, "{:.1} KB", self.0 as f64 / 1024.0)
        } else {
            write!(f, "{} B", self.0)
        }
    }
}

/// Percentage value (0.0 to 100.0)
#[derive(Debug, Clone, Copy, PartialEq, PartialOrd, Serialize, Deserialize)]
pub struct Percentage(f64);

impl Percentage {
    /// Create a new percentage value
    pub fn new(value: f64) -> Option<Self> {
        if (0.0..=100.0).contains(&value) {
            Some(Self(value))
        } else {
            None
        }
    }

    /// Get the percentage value
    pub fn value(&self) -> f64 {
        self.0
    }

    /// Get the percentage as a ratio (0.0 to 1.0)
    pub fn as_ratio(&self) -> f64 {
        self.0 / 100.0
    }
}

impl fmt::Display for Percentage {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{:.1}%", self.0)
    }
}

/// Component identifier
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct ComponentId(heapless::String<64>);

impl ComponentId {
    /// Create a new component ID
    pub fn new(id: &str) -> Option<Self> {
        heapless::String::try_from(id).ok().map(Self)
    }

    /// Get the ID as a string slice
    pub fn as_str(&self) -> &str {
        &self.0
    }
}

impl fmt::Display for ComponentId {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.0)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_mac_address() {
        let mac = MacAddress::new([0x00, 0x11, 0x22, 0x33, 0x44, 0x55]);
        assert_eq!(format!("{}", mac), "00:11:22:33:44:55");
        assert!(mac.is_unicast());
        assert!(!mac.is_multicast());
        assert!(!mac.is_broadcast());

        let broadcast = MacAddress::new([0xff; 6]);
        assert!(broadcast.is_broadcast());
        assert!(broadcast.is_multicast());
    }

    #[test]
    fn test_duration() {
        let duration = Duration::from_secs(5);
        assert_eq!(duration.as_millis(), 5000);
        assert_eq!(duration.as_secs(), 5);

        let duration_ms = Duration::from_millis(1500);
        assert_eq!(duration_ms.as_secs(), 1);
        assert_eq!(duration_ms.as_millis(), 1500);
    }

    #[test]
    fn test_size() {
        let size = Size::from_mb(1);
        assert_eq!(size.as_bytes(), 1024 * 1024);
        assert_eq!(size.as_kb(), 1024);
        assert_eq!(size.as_mb(), 1);

        let size_kb = Size::from_kb(1);
        assert_eq!(size_kb.as_bytes(), 1024);
    }

    #[test]
    fn test_percentage() {
        let pct = Percentage::new(75.5).unwrap();
        assert_eq!(pct.value(), 75.5);
        assert_eq!(pct.as_ratio(), 0.755);
        assert_eq!(format!("{}", pct), "75.5%");

        assert!(Percentage::new(101.0).is_none());
        assert!(Percentage::new(-1.0).is_none());
    }

    #[test]
    fn test_component_id() {
        let id = ComponentId::new("test-component").unwrap();
        assert_eq!(id.as_str(), "test-component");
        assert_eq!(format!("{}", id), "test-component");
    }
}
