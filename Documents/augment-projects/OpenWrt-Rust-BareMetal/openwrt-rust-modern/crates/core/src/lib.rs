//! OpenWrt Core Abstractions
//!
//! This crate provides the fundamental abstractions and traits that form the foundation
//! of the modernized OpenWrt Rust implementation. It defines core interfaces for:
//!
//! - Configuration management
//! - Network interfaces
//! - Event handling
//! - Error handling
//! - Resource management
//!
//! # Design Principles
//!
//! - **Trait-based architecture**: Clear interfaces and abstractions
//! - **Type safety**: Leverage Rust's type system for correctness
//! - **Async-first**: Modern async/await patterns for I/O operations
//! - **No-std compatibility**: Support for embedded environments
//! - **Composability**: Small, focused traits that can be combined

#![cfg_attr(not(feature = "std"), no_std)]
#![deny(missing_docs)]
#![deny(unsafe_code)]

extern crate alloc;

pub mod config;
pub mod error;
pub mod event;
pub mod network;
pub mod resource;
pub mod traits;
pub mod types;

// Re-export commonly used types
pub use error::{Error, Result};
pub use event::{Event, EventHandler, EventLoop};
pub use network::{NetworkInterface, NetworkManager};
pub use resource::{Resource, ResourceManager};
pub use traits::*;
pub use types::*;

/// OpenWrt core version information
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

/// OpenWrt core build information
pub const BUILD_INFO: &str = concat!(
    "OpenWrt Core v",
    env!("CARGO_PKG_VERSION")
);

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_version_info() {
        assert!(!VERSION.is_empty());
        assert!(!BUILD_INFO.is_empty());
    }
}
