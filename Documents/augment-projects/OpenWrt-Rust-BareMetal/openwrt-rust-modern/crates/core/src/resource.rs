//! Resource management abstractions

use crate::{<PERSON><PERSON><PERSON>, Result, ComponentId};
use serde::{Deserialize, Serialize};

/// Resource trait
pub trait Resource {
    /// Resource identifier
    fn id(&self) -> &str;

    /// Acquire the resource
    fn acquire(&mut self) -> Result<()>;

    /// Release the resource
    fn release(&mut self) -> Result<()>;

    /// Check if resource is available
    fn is_available(&self) -> bool;

    /// Get resource usage information
    fn usage(&self) -> ResourceUsage;
}

/// Resource usage information
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ResourceUsage {
    /// Resource identifier
    pub id: heapless::String<64>,
    /// Current usage amount
    pub current: u64,
    /// Maximum available
    pub maximum: u64,
    /// Usage percentage
    pub percentage: f64,
}

/// Resource manager trait
pub trait ResourceManager {
    /// Register a resource
    fn register_resource(&mut self, resource: Box<dyn Resource>) -> Result<()>;

    /// Unregister a resource
    fn unregister_resource(&mut self, id: &str) -> Result<()>;

    /// Get resource by ID
    fn get_resource(&self, id: &str) -> Result<&dyn Resource>;

    /// List all resources
    fn list_resources(&self) -> heapless::Vec<heapless::String<64>, 32>;

    /// Get resource usage summary
    fn usage_summary(&self) -> Result<heapless::Vec<ResourceUsage, 32>>;
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_resource_usage() {
        let usage = ResourceUsage {
            id: heapless::String::try_from("memory").unwrap(),
            current: 1024,
            maximum: 2048,
            percentage: 50.0,
        };

        let serialized = serde_json::to_string(&usage).unwrap();
        let deserialized: ResourceUsage = serde_json::from_str(&serialized).unwrap();
        assert_eq!(usage, deserialized);
    }
}
