[package]
name = "openwrt-core"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
description = "Core abstractions and traits for OpenWrt Rust implementation"
keywords.workspace = true
categories.workspace = true

[dependencies]
# Core dependencies
serde = { workspace = true, features = ["derive"] }
thiserror = { workspace = true }
log = { workspace = true }
bitflags = { workspace = true }
heapless = { workspace = true }

# Async support
tokio = { workspace = true, features = ["rt", "macros", "time"], optional = true }
futures = { workspace = true, optional = true }

# Embedded support
embedded-hal = { workspace = true, optional = true }
embedded-io = { workspace = true, optional = true }

[dev-dependencies]
tokio = { workspace = true, features = ["rt", "macros", "time", "test-util"] }
proptest = { workspace = true }
criterion = { workspace = true }

[features]
default = ["std"]
std = ["tokio", "futures"]
embedded = ["embedded-hal", "embedded-io"]
async = ["tokio", "futures"]

[[bench]]
name = "core_benchmarks"
harness = false
