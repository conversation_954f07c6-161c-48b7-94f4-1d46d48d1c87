//! OpenWrt Inter-Process Communication
//!
//! This crate provides a modern, type-safe IPC system for communication between
//! OpenWrt components. It supports multiple transport mechanisms and serialization
//! formats while maintaining high performance and security.
//!
//! # Features
//!
//! - **Multiple Transports**: Unix domain sockets, TCP, shared memory
//! - **Type Safety**: Strongly typed message definitions
//! - **Async Support**: Full async/await support for non-blocking operations
//! - **Security**: Optional encryption and authentication
//! - **Performance**: Zero-copy serialization where possible
//! - **Reliability**: Message acknowledgment and retry mechanisms
//!
//! # Architecture
//!
//! The IPC system consists of several key components:
//!
//! - **MessageBus**: Central message routing and delivery
//! - **Endpoints**: Connection points for services
//! - **Channels**: Typed communication channels
//! - **Serializers**: Message serialization/deserialization
//! - **Transports**: Underlying communication mechanisms
//!
//! # Example
//!
//! ```rust
//! use openwrt_ipc::{MessageBus, Endpoint, Message};
//!
//! // Create a message bus
//! let mut bus = MessageBus::new().await?;
//!
//! // Create an endpoint
//! let endpoint = Endpoint::new("network-manager").await?;
//! bus.register_endpoint(endpoint).await?;
//!
//! // Send a message
//! let message = Message::new("interface.configure", data);
//! bus.send_message("network-manager", message).await?;
//! ```

#![cfg_attr(not(feature = "std"), no_std)]
#![deny(missing_docs)]

extern crate alloc;

pub mod bus;
pub mod endpoint;
pub mod message;
pub mod transport;
pub mod serialization;
pub mod security;

#[cfg(feature = "unix-sockets")]
pub mod unix;

// Re-export commonly used types
pub use bus::{MessageBus, MessageBusConfig};
pub use endpoint::{Endpoint, EndpointConfig};
pub use message::{Message, MessageHeader, MessageType, MessageId};
pub use transport::{Transport, TransportType};
pub use serialization::{Serializer, SerializationFormat};
pub use openwrt_core::{Error, Result};

/// IPC library version
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

/// Default IPC configuration
pub mod defaults {
    /// Default socket directory
    pub const SOCKET_DIR: &str = "/var/run/openwrt";
    /// Default message timeout in milliseconds
    pub const MESSAGE_TIMEOUT: u64 = 5000;
    /// Default retry count
    pub const RETRY_COUNT: u32 = 3;
    /// Default buffer size
    pub const BUFFER_SIZE: usize = 8192;
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_version() {
        assert!(!VERSION.is_empty());
    }

    #[test]
    fn test_defaults() {
        assert_eq!(defaults::SOCKET_DIR, "/var/run/openwrt");
        assert_eq!(defaults::MESSAGE_TIMEOUT, 5000);
        assert_eq!(defaults::RETRY_COUNT, 3);
        assert_eq!(defaults::BUFFER_SIZE, 8192);
    }
}
