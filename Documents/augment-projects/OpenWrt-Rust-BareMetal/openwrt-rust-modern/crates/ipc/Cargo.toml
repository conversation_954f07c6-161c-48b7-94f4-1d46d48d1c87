[package]
name = "openwrt-ipc"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
description = "Modern IPC system for OpenWrt components"
keywords.workspace = true
categories.workspace = true

[dependencies]
# Core dependencies
openwrt-core = { path = "../core" }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
postcard = { workspace = true }
thiserror = { workspace = true }
log = { workspace = true }
heapless = { workspace = true }
bitflags = { workspace = true }

# Async support
tokio = { workspace = true, features = ["rt", "net", "time", "macros", "sync", "io-util"], optional = true }
futures = { workspace = true, optional = true }

# System integration
libc = { version = "0.2", optional = true }
nix = { version = "0.27", features = ["socket", "uio"], optional = true }

# Cryptography for secure IPC
ring = { version = "0.17", optional = true }

[dev-dependencies]
tokio = { workspace = true, features = ["rt", "macros", "time", "test-util"] }
tempfile = "3.8"

[features]
default = ["std", "async", "unix-sockets"]
std = ["tokio", "futures", "libc", "nix"]
async = ["tokio", "futures"]
unix-sockets = ["nix"]
secure = ["ring"]

[lib]
name = "openwrt_ipc"
path = "src/lib.rs"
