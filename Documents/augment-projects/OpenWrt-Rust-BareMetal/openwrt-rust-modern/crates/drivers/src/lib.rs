//! OpenWrt Hardware Abstraction Layer and Drivers
//!
//! This crate provides a comprehensive hardware abstraction layer (HAL) and
//! device drivers for OpenWrt systems. It supports various hardware platforms
//! and provides a unified interface for hardware access.
//!
//! # Features
//!
//! - **Platform Abstraction**: Unified interface across different hardware platforms
//! - **Device Drivers**: Support for common embedded peripherals
//! - **Memory Safety**: Safe hardware access through Rust's type system
//! - **Async Support**: Non-blocking hardware operations
//! - **Hot-Plug Support**: Dynamic device detection and management
//! - **Power Management**: Advanced power management features
//!
//! # Supported Hardware
//!
//! - **Network Interfaces**: Ethernet, WiFi, cellular modems
//! - **GPIO**: General-purpose input/output pins
//! - **SPI/I2C**: Serial communication interfaces
//! - **USB**: USB host and device support
//! - **Storage**: Flash, eMMC, SD cards
//! - **Sensors**: Temperature, voltage, current monitoring
//!
//! # Example
//!
//! ```rust
//! use openwrt_drivers::{DriverManager, NetworkDriver, GpioDriver};
//!
//! // Initialize driver manager
//! let mut manager = DriverManager::new().await?;
//!
//! // Load network driver
//! let net_driver = manager.load_driver::<NetworkDriver>("eth0").await?;
//!
//! // Configure GPIO
//! let gpio = manager.get_gpio_driver()?;
//! gpio.set_pin_mode(18, PinMode::Output)?;
//! gpio.write_pin(18, true)?;
//! ```

#![cfg_attr(not(feature = "std"), no_std)]
#![deny(missing_docs)]

extern crate alloc;

pub mod manager;
pub mod network;
pub mod gpio;
pub mod spi;
pub mod i2c;
pub mod usb;
pub mod storage;
pub mod sensors;
pub mod power;

#[cfg(feature = "system-drivers")]
pub mod system;

// Re-export commonly used types
pub use manager::{DriverManager, DriverConfig, DriverInfo};
pub use network::{NetworkDriver, NetworkInterface, LinkState};
pub use gpio::{GpioDriver, PinMode, PinState};
pub use spi::{SpiDriver, SpiConfig, SpiMode};
pub use i2c::{I2cDriver, I2cConfig};
pub use usb::{UsbDriver, UsbDevice, UsbDeviceType};
pub use storage::{StorageDriver, StorageDevice, StorageType};
pub use sensors::{SensorDriver, SensorReading, SensorType};
pub use power::{PowerManager, PowerState, PowerPolicy};
pub use openwrt_core::{Error, Result};

/// Driver library version
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

/// Hardware platform information
#[derive(Debug, Clone, PartialEq, serde::Serialize, serde::Deserialize)]
pub struct PlatformInfo {
    /// Platform name
    pub name: heapless::String<64>,
    /// Architecture
    pub architecture: heapless::String<32>,
    /// CPU model
    pub cpu_model: heapless::String<128>,
    /// CPU frequency in MHz
    pub cpu_frequency: u32,
    /// Total RAM in MB
    pub total_ram: u32,
    /// Available RAM in MB
    pub available_ram: u32,
    /// Flash size in MB
    pub flash_size: u32,
    /// Board revision
    pub board_revision: heapless::String<32>,
    /// Kernel version
    pub kernel_version: heapless::String<64>,
}

impl Default for PlatformInfo {
    fn default() -> Self {
        Self {
            name: heapless::String::try_from("Unknown").unwrap(),
            architecture: heapless::String::try_from("Unknown").unwrap(),
            cpu_model: heapless::String::try_from("Unknown").unwrap(),
            cpu_frequency: 0,
            total_ram: 0,
            available_ram: 0,
            flash_size: 0,
            board_revision: heapless::String::try_from("Unknown").unwrap(),
            kernel_version: heapless::String::try_from("Unknown").unwrap(),
        }
    }
}

/// Driver types
#[derive(Debug, Clone, Copy, PartialEq, Eq, serde::Serialize, serde::Deserialize)]
pub enum DriverType {
    /// Network interface driver
    Network,
    /// GPIO driver
    Gpio,
    /// SPI driver
    Spi,
    /// I2C driver
    I2c,
    /// USB driver
    Usb,
    /// Storage driver
    Storage,
    /// Sensor driver
    Sensor,
    /// Power management driver
    Power,
    /// Custom driver
    Custom,
}

/// Driver state
#[derive(Debug, Clone, Copy, PartialEq, Eq, serde::Serialize, serde::Deserialize)]
pub enum DriverState {
    /// Driver is unloaded
    Unloaded,
    /// Driver is loading
    Loading,
    /// Driver is loaded and ready
    Loaded,
    /// Driver is active
    Active,
    /// Driver has failed
    Failed,
    /// Driver is unloading
    Unloading,
}

/// Hardware capabilities
bitflags::bitflags! {
    /// Hardware capability flags
    #[derive(Debug, Clone, Copy, PartialEq, Eq, serde::Serialize, serde::Deserialize)]
    pub struct HardwareCapabilities: u32 {
        /// Ethernet support
        const ETHERNET = 1 << 0;
        /// WiFi support
        const WIFI = 1 << 1;
        /// Bluetooth support
        const BLUETOOTH = 1 << 2;
        /// USB host support
        const USB_HOST = 1 << 3;
        /// USB device support
        const USB_DEVICE = 1 << 4;
        /// GPIO support
        const GPIO = 1 << 5;
        /// SPI support
        const SPI = 1 << 6;
        /// I2C support
        const I2C = 1 << 7;
        /// UART support
        const UART = 1 << 8;
        /// PWM support
        const PWM = 1 << 9;
        /// ADC support
        const ADC = 1 << 10;
        /// Hardware watchdog
        const WATCHDOG = 1 << 11;
        /// Hardware RNG
        const HARDWARE_RNG = 1 << 12;
        /// Crypto acceleration
        const CRYPTO_ACCEL = 1 << 13;
        /// Power management
        const POWER_MGMT = 1 << 14;
        /// Temperature sensors
        const TEMP_SENSORS = 1 << 15;
    }
}

impl Default for HardwareCapabilities {
    fn default() -> Self {
        Self::empty()
    }
}

/// Driver error types
#[derive(Debug, thiserror::Error)]
pub enum DriverError {
    /// Driver not found
    #[error("Driver not found: {0}")]
    NotFound(String),
    
    /// Driver already loaded
    #[error("Driver already loaded: {0}")]
    AlreadyLoaded(String),
    
    /// Hardware not available
    #[error("Hardware not available: {0}")]
    HardwareUnavailable(String),
    
    /// Permission denied
    #[error("Permission denied: {0}")]
    PermissionDenied(String),
    
    /// Device busy
    #[error("Device busy: {0}")]
    DeviceBusy(String),
    
    /// Invalid configuration
    #[error("Invalid configuration: {0}")]
    InvalidConfig(String),
    
    /// Hardware error
    #[error("Hardware error: {0}")]
    Hardware(String),
}

impl From<DriverError> for openwrt_core::Error {
    fn from(err: DriverError) -> Self {
        openwrt_core::Error::Driver(err.to_string())
    }
}

/// Device information
#[derive(Debug, Clone, PartialEq, serde::Serialize, serde::Deserialize)]
pub struct DeviceInfo {
    /// Device name
    pub name: heapless::String<64>,
    /// Device type
    pub device_type: DriverType,
    /// Vendor ID
    pub vendor_id: Option<u16>,
    /// Product ID
    pub product_id: Option<u16>,
    /// Device path
    pub device_path: heapless::String<256>,
    /// Driver name
    pub driver_name: heapless::String<64>,
    /// Device state
    pub state: DriverState,
    /// Capabilities
    pub capabilities: HardwareCapabilities,
}

impl Default for DeviceInfo {
    fn default() -> Self {
        Self {
            name: heapless::String::new(),
            device_type: DriverType::Custom,
            vendor_id: None,
            product_id: None,
            device_path: heapless::String::new(),
            driver_name: heapless::String::new(),
            state: DriverState::Unloaded,
            capabilities: HardwareCapabilities::default(),
        }
    }
}

/// Driver statistics
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct DriverStatistics {
    /// Total operations
    pub total_operations: u64,
    /// Successful operations
    pub successful_operations: u64,
    /// Failed operations
    pub failed_operations: u64,
    /// Bytes transferred
    pub bytes_transferred: u64,
    /// Last operation timestamp
    pub last_operation: Option<u64>,
    /// Driver uptime in seconds
    pub uptime: u64,
}

impl Default for DriverStatistics {
    fn default() -> Self {
        Self {
            total_operations: 0,
            successful_operations: 0,
            failed_operations: 0,
            bytes_transferred: 0,
            last_operation: None,
            uptime: 0,
        }
    }
}

/// Get platform information
pub fn get_platform_info() -> Result<PlatformInfo> {
    #[cfg(feature = "system-drivers")]
    {
        system::get_platform_info()
    }
    
    #[cfg(not(feature = "system-drivers"))]
    {
        Ok(PlatformInfo::default())
    }
}

/// Detect hardware capabilities
pub fn detect_hardware_capabilities() -> Result<HardwareCapabilities> {
    #[cfg(feature = "system-drivers")]
    {
        system::detect_capabilities()
    }
    
    #[cfg(not(feature = "system-drivers"))]
    {
        Ok(HardwareCapabilities::default())
    }
}

/// Initialize driver subsystem
pub fn init_drivers() -> Result<()> {
    log::info!("Initializing OpenWrt driver subsystem");
    
    // Detect platform
    let platform = get_platform_info()?;
    log::info!("Platform: {} ({})", platform.name, platform.architecture);
    
    // Detect capabilities
    let capabilities = detect_hardware_capabilities()?;
    log::info!("Hardware capabilities: {:?}", capabilities);
    
    // Initialize driver manager
    manager::init_driver_manager()?;
    
    log::info!("Driver subsystem initialized successfully");
    Ok(())
}

/// Shutdown driver subsystem
pub fn shutdown_drivers() -> Result<()> {
    log::info!("Shutting down OpenWrt driver subsystem");
    
    // Shutdown driver manager
    manager::shutdown_driver_manager()?;
    
    log::info!("Driver subsystem shutdown complete");
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_version() {
        assert!(!VERSION.is_empty());
    }

    #[test]
    fn test_driver_types() {
        assert_eq!(DriverType::Network as u8, 0);
        assert_ne!(DriverType::Network, DriverType::Gpio);
    }

    #[test]
    fn test_driver_states() {
        assert_eq!(DriverState::Unloaded as u8, 0);
        assert_ne!(DriverState::Loaded, DriverState::Failed);
    }

    #[test]
    fn test_hardware_capabilities() {
        let caps = HardwareCapabilities::ETHERNET | HardwareCapabilities::WIFI;
        assert!(caps.contains(HardwareCapabilities::ETHERNET));
        assert!(caps.contains(HardwareCapabilities::WIFI));
        assert!(!caps.contains(HardwareCapabilities::BLUETOOTH));
    }

    #[test]
    fn test_platform_info() {
        let platform = PlatformInfo::default();
        assert_eq!(platform.name, "Unknown");
        assert_eq!(platform.architecture, "Unknown");
        assert_eq!(platform.cpu_frequency, 0);
    }

    #[test]
    fn test_device_info() {
        let device = DeviceInfo::default();
        assert_eq!(device.state, DriverState::Unloaded);
        assert_eq!(device.device_type, DriverType::Custom);
        assert!(device.name.is_empty());
    }

    #[test]
    fn test_driver_statistics() {
        let stats = DriverStatistics::default();
        assert_eq!(stats.total_operations, 0);
        assert_eq!(stats.successful_operations, 0);
        assert_eq!(stats.failed_operations, 0);
        assert_eq!(stats.bytes_transferred, 0);
    }
}
