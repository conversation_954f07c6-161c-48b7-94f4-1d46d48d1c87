[package]
name = "openwrt-drivers"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
description = "Hardware abstraction layer and drivers for OpenWrt"
keywords.workspace = true
categories.workspace = true

[dependencies]
# Core dependencies
openwrt-core = { path = "../core" }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
thiserror = { workspace = true }
log = { workspace = true }
heapless = { workspace = true }
bitflags = { workspace = true }

# Embedded HAL
embedded-hal = { version = "1.0" }
nb = { version = "1.1" }

# Async support
tokio = { workspace = true, features = ["rt", "time", "macros"], optional = true }
futures = { workspace = true, optional = true }

# System integration
libc = { version = "0.2", optional = true }
nix = { version = "0.27", features = ["ioctl"], optional = true }

# Memory mapping
memmap2 = { version = "0.9", optional = true }

# GPIO and SPI
rppal = { version = "0.14", optional = true }

[dev-dependencies]
tokio = { workspace = true, features = ["rt", "macros", "time", "test-util"] }

[features]
default = ["std", "async"]
std = ["tokio", "futures", "libc", "nix", "memmap2"]
async = ["tokio", "futures"]
gpio = ["rppal"]
spi = ["rppal"]
i2c = ["rppal"]
system-drivers = ["libc", "nix", "memmap2"]

[lib]
name = "openwrt_drivers"
path = "src/lib.rs"
