[package]
name = "openwrt-config"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
description = "Modern type-safe configuration management for OpenWrt"
keywords.workspace = true
categories.workspace = true

[dependencies]
# Core dependencies
openwrt-core = { path = "../core" }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
toml = { workspace = true }
postcard = { workspace = true }
thiserror = { workspace = true }
log = { workspace = true }
heapless = { workspace = true }

# Parser for UCI format
nom = { workspace = true }

# File system operations (std only)
walkdir = { version = "2.4", optional = true }

[dev-dependencies]
tempfile = "3.8"
proptest = { workspace = true }

[features]
default = ["std"]
std = ["walkdir"]
uci-compat = []

[lib]
name = "openwrt_config"
path = "src/lib.rs"
