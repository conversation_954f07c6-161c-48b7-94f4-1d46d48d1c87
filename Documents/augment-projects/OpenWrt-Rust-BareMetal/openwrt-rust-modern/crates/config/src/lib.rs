//! OpenWrt Configuration Management
//!
//! This crate provides a modern, type-safe configuration management system
//! that replaces the traditional UCI (Unified Configuration Interface) with
//! a more robust and maintainable solution.
//!
//! # Features
//!
//! - **Type Safety**: Strong typing for all configuration values
//! - **Validation**: Comprehensive validation with detailed error messages
//! - **Serialization**: Support for JSON, TOML, and binary formats
//! - **Migration**: Automatic migration from UCI format
//! - **Atomic Updates**: Transactional configuration changes
//! - **Backup/Restore**: Built-in backup and restore functionality
//!
//! # Example
//!
//! ```rust
//! use openwrt_config::{ConfigManager, NetworkConfig};
//!
//! let mut manager = ConfigManager::new("/etc/openwrt")?;
//! let network_config: NetworkConfig = manager.load("network")?;
//! 
//! // Modify configuration
//! let mut new_config = network_config.clone();
//! new_config.interfaces.push(/* new interface */);
//!
//! // Validate and save
//! manager.validate(&new_config)?;
//! manager.save("network", &new_config)?;
//! ```

#![cfg_attr(not(feature = "std"), no_std)]
#![deny(missing_docs)]

extern crate alloc;

pub mod manager;
pub mod schema;
pub mod validation;
pub mod migration;
pub mod backup;
pub mod formats;

#[cfg(feature = "uci-compat")]
pub mod uci;

// Re-export commonly used types
pub use manager::{ConfigManager, ConfigTransaction};
pub use schema::*;
pub use validation::{ValidationError, ValidationResult, Validator};
pub use openwrt_core::{Error, Result};

/// Configuration format types
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ConfigFormat {
    /// JSON format
    Json,
    /// TOML format
    Toml,
    /// Binary format (postcard)
    Binary,
    /// UCI format (legacy)
    #[cfg(feature = "uci-compat")]
    Uci,
}

impl ConfigFormat {
    /// Get file extension for this format
    pub fn extension(&self) -> &'static str {
        match self {
            ConfigFormat::Json => "json",
            ConfigFormat::Toml => "toml",
            ConfigFormat::Binary => "bin",
            #[cfg(feature = "uci-compat")]
            ConfigFormat::Uci => "uci",
        }
    }

    /// Detect format from file extension
    pub fn from_extension(ext: &str) -> Option<Self> {
        match ext.to_lowercase().as_str() {
            "json" => Some(ConfigFormat::Json),
            "toml" => Some(ConfigFormat::Toml),
            "bin" => Some(ConfigFormat::Binary),
            #[cfg(feature = "uci-compat")]
            "uci" => Some(ConfigFormat::Uci),
            _ => None,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_config_format_extension() {
        assert_eq!(ConfigFormat::Json.extension(), "json");
        assert_eq!(ConfigFormat::Toml.extension(), "toml");
        assert_eq!(ConfigFormat::Binary.extension(), "bin");
    }

    #[test]
    fn test_config_format_from_extension() {
        assert_eq!(ConfigFormat::from_extension("json"), Some(ConfigFormat::Json));
        assert_eq!(ConfigFormat::from_extension("JSON"), Some(ConfigFormat::Json));
        assert_eq!(ConfigFormat::from_extension("toml"), Some(ConfigFormat::Toml));
        assert_eq!(ConfigFormat::from_extension("bin"), Some(ConfigFormat::Binary));
        assert_eq!(ConfigFormat::from_extension("unknown"), None);
    }
}
