//! Configuration schema definitions
//!
//! This module defines the type-safe configuration schemas for all OpenWrt components.
//! These schemas replace the loosely-typed UCI configuration with strongly-typed
//! Rust structures that provide compile-time guarantees and runtime validation.

use serde::{Deserialize, Serialize};
use openwrt_core::types::*;

/// Root configuration containing all system configuration
#[derive(Debu<PERSON>, <PERSON>lone, <PERSON>ialEq, Serialize, Deserialize)]
pub struct SystemConfig {
    /// Network configuration
    pub network: NetworkConfig,
    /// System configuration
    pub system: SystemSettings,
    /// Wireless configuration
    pub wireless: WirelessConfig,
    /// Firewall configuration
    pub firewall: FirewallConfig,
    /// DHCP configuration
    pub dhcp: DhcpConfig,
}

/// Network configuration
#[derive(Debug, <PERSON><PERSON>, PartialEq, Serialize, Deserialize)]
pub struct NetworkConfig {
    /// Network interfaces
    pub interfaces: heapless::Vec<InterfaceConfig, 32>,
    /// Static routes
    pub routes: heapless::Vec<RouteConfig, 64>,
    /// Global network settings
    pub globals: NetworkGlobals,
}

/// Network interface configuration
#[derive(Debug, <PERSON><PERSON>, <PERSON>ial<PERSON>q, Serialize, Deserialize)]
pub struct InterfaceConfig {
    /// Interface name
    pub name: heapless::String<32>,
    /// Interface type
    pub interface_type: InterfaceType,
    /// Protocol configuration
    pub protocol: ProtocolConfig,
    /// Physical device
    pub device: Option<heapless::String<32>>,
    /// MAC address override
    pub mac_address: Option<MacAddress>,
    /// MTU size
    pub mtu: Option<u16>,
    /// Whether interface is enabled
    pub enabled: bool,
    /// Interface metric
    pub metric: Option<u32>,
    /// VLAN configuration (for VLAN interfaces)
    pub vlan: Option<VlanConfig>,
    /// Bridge configuration (for bridge interfaces)
    pub bridge: Option<BridgeConfig>,
}

/// Protocol configuration
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ProtocolConfig {
    /// Static IP configuration
    Static {
        /// IP address
        address: heapless::String<64>,
        /// Network mask
        netmask: heapless::String<64>,
        /// Gateway
        gateway: Option<heapless::String<64>>,
        /// DNS servers
        dns: heapless::Vec<heapless::String<64>, 4>,
    },
    /// DHCP client configuration
    Dhcp {
        /// Hostname to send
        hostname: Option<heapless::String<64>>,
        /// Client identifier
        client_id: Option<heapless::String<64>>,
        /// Vendor class
        vendor_class: Option<heapless::String<64>>,
        /// Request specific options
        request_options: heapless::Vec<u8, 16>,
    },
    /// PPPoE configuration
    Pppoe {
        /// Username
        username: heapless::String<64>,
        /// Password
        password: heapless::String<64>,
        /// Service name
        service: Option<heapless::String<64>>,
        /// Access concentrator
        ac: Option<heapless::String<64>>,
    },
    /// No protocol (interface only)
    None,
}

/// VLAN configuration
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct VlanConfig {
    /// VLAN ID
    pub id: u16,
    /// Parent interface
    pub parent: heapless::String<32>,
}

/// Bridge configuration
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct BridgeConfig {
    /// Bridge ports
    pub ports: heapless::Vec<heapless::String<32>, 16>,
    /// STP enabled
    pub stp: bool,
    /// Forward delay
    pub forward_delay: Option<u16>,
    /// Hello time
    pub hello_time: Option<u16>,
    /// Max age
    pub max_age: Option<u16>,
}

/// Route configuration
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct RouteConfig {
    /// Destination network
    pub destination: heapless::String<64>,
    /// Gateway
    pub gateway: heapless::String<64>,
    /// Metric
    pub metric: Option<u32>,
    /// Interface
    pub interface: Option<heapless::String<32>>,
    /// Route table
    pub table: Option<heapless::String<32>>,
}

/// Global network settings
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct NetworkGlobals {
    /// Enable IPv6
    pub ipv6: bool,
    /// Enable IP forwarding
    pub ip_forward: bool,
    /// Enable IPv6 forwarding
    pub ipv6_forward: bool,
    /// Default gateway
    pub default_gateway: Option<heapless::String<64>>,
    /// Default IPv6 gateway
    pub default_gateway_v6: Option<heapless::String<64>>,
}

/// System settings
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct SystemSettings {
    /// System hostname
    pub hostname: heapless::String<64>,
    /// System timezone
    pub timezone: heapless::String<64>,
    /// Log level
    pub log_level: LogLevel,
    /// Log destination
    pub log_destination: LogDestination,
    /// NTP servers
    pub ntp_servers: heapless::Vec<heapless::String<64>, 4>,
    /// Enable NTP client
    pub ntp_enabled: bool,
}

/// Log level
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum LogLevel {
    /// Emergency
    Emergency,
    /// Alert
    Alert,
    /// Critical
    Critical,
    /// Error
    Error,
    /// Warning
    Warning,
    /// Notice
    Notice,
    /// Info
    Info,
    /// Debug
    Debug,
}

/// Log destination
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum LogDestination {
    /// System log
    Syslog,
    /// File
    File(heapless::String<256>),
    /// Remote syslog
    Remote {
        /// Server address
        server: heapless::String<64>,
        /// Port
        port: u16,
        /// Protocol
        protocol: LogProtocol,
    },
}

/// Log protocol
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum LogProtocol {
    /// UDP
    Udp,
    /// TCP
    Tcp,
    /// TLS
    Tls,
}

/// Wireless configuration
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct WirelessConfig {
    /// Wireless devices
    pub devices: heapless::Vec<WirelessDevice, 8>,
    /// Wireless interfaces
    pub interfaces: heapless::Vec<WirelessInterface, 16>,
}

/// Wireless device configuration
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct WirelessDevice {
    /// Device name
    pub name: heapless::String<32>,
    /// Device type
    pub device_type: heapless::String<32>,
    /// Channel
    pub channel: Option<u8>,
    /// TX power
    pub tx_power: Option<u8>,
    /// Country code
    pub country: Option<heapless::String<4>>,
    /// Disabled
    pub disabled: bool,
}

/// Wireless interface configuration
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct WirelessInterface {
    /// Interface name
    pub name: heapless::String<32>,
    /// Device
    pub device: heapless::String<32>,
    /// Mode
    pub mode: WirelessMode,
    /// SSID
    pub ssid: heapless::String<64>,
    /// Encryption
    pub encryption: WirelessEncryption,
    /// Network
    pub network: heapless::String<32>,
    /// Disabled
    pub disabled: bool,
}

/// Wireless mode
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum WirelessMode {
    /// Access Point
    Ap,
    /// Station
    Sta,
    /// Ad-hoc
    Adhoc,
    /// Monitor
    Monitor,
}

/// Wireless encryption
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum WirelessEncryption {
    /// No encryption
    None,
    /// WEP
    Wep {
        /// Key
        key: heapless::String<64>,
    },
    /// WPA/WPA2 PSK
    WpaPsk {
        /// Key
        key: heapless::String<64>,
        /// Cipher
        cipher: WpaCipher,
    },
    /// WPA/WPA2 Enterprise
    WpaEap {
        /// Server
        server: heapless::String<64>,
        /// Port
        port: u16,
        /// Identity
        identity: heapless::String<64>,
        /// Password
        password: heapless::String<64>,
    },
}

/// WPA cipher
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum WpaCipher {
    /// TKIP
    Tkip,
    /// CCMP (AES)
    Ccmp,
    /// TKIP+CCMP
    TkipCcmp,
}

/// Firewall configuration
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct FirewallConfig {
    /// Firewall zones
    pub zones: heapless::Vec<FirewallZone, 16>,
    /// Firewall rules
    pub rules: heapless::Vec<FirewallRule, 64>,
    /// Port forwards
    pub forwards: heapless::Vec<PortForward, 32>,
}

/// Firewall zone
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct FirewallZone {
    /// Zone name
    pub name: heapless::String<32>,
    /// Networks in this zone
    pub networks: heapless::Vec<heapless::String<32>, 8>,
    /// Default input policy
    pub input: FirewallPolicy,
    /// Default output policy
    pub output: FirewallPolicy,
    /// Default forward policy
    pub forward: FirewallPolicy,
    /// Enable masquerading
    pub masq: bool,
}

/// Firewall policy
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum FirewallPolicy {
    /// Accept
    Accept,
    /// Reject
    Reject,
    /// Drop
    Drop,
}

/// Firewall rule
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct FirewallRule {
    /// Rule name
    pub name: heapless::String<64>,
    /// Source zone
    pub src: Option<heapless::String<32>>,
    /// Destination zone
    pub dest: Option<heapless::String<32>>,
    /// Source address
    pub src_ip: Option<heapless::String<64>>,
    /// Destination address
    pub dest_ip: Option<heapless::String<64>>,
    /// Source port
    pub src_port: Option<heapless::String<32>>,
    /// Destination port
    pub dest_port: Option<heapless::String<32>>,
    /// Protocol
    pub proto: Option<heapless::String<16>>,
    /// Target action
    pub target: FirewallPolicy,
}

/// Port forward configuration
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct PortForward {
    /// Forward name
    pub name: heapless::String<64>,
    /// Source zone
    pub src: heapless::String<32>,
    /// External port
    pub src_dport: heapless::String<32>,
    /// Destination address
    pub dest_ip: heapless::String<64>,
    /// Destination port
    pub dest_port: Option<heapless::String<32>>,
    /// Protocol
    pub proto: heapless::String<16>,
}

/// DHCP configuration
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct DhcpConfig {
    /// DHCP pools
    pub pools: heapless::Vec<DhcpPool, 16>,
    /// Static leases
    pub static_leases: heapless::Vec<DhcpStaticLease, 64>,
    /// Global DHCP settings
    pub globals: DhcpGlobals,
}

/// DHCP pool configuration
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct DhcpPool {
    /// Interface
    pub interface: heapless::String<32>,
    /// Start address
    pub start: heapless::String<64>,
    /// End address
    pub limit: u32,
    /// Lease time
    pub leasetime: heapless::String<32>,
    /// DNS servers
    pub dns: heapless::Vec<heapless::String<64>, 4>,
    /// Domain name
    pub domain: Option<heapless::String<64>>,
}

/// DHCP static lease
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct DhcpStaticLease {
    /// MAC address
    pub mac: MacAddress,
    /// IP address
    pub ip: heapless::String<64>,
    /// Hostname
    pub name: Option<heapless::String<64>>,
}

/// Global DHCP settings
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct DhcpGlobals {
    /// Enable DHCP server
    pub enabled: bool,
    /// Authoritative
    pub authoritative: bool,
    /// Boot filename
    pub boot_file: Option<heapless::String<128>>,
    /// TFTP server
    pub tftp_server: Option<heapless::String<64>>,
}

impl Default for SystemConfig {
    fn default() -> Self {
        Self {
            network: NetworkConfig::default(),
            system: SystemSettings::default(),
            wireless: WirelessConfig::default(),
            firewall: FirewallConfig::default(),
            dhcp: DhcpConfig::default(),
        }
    }
}

impl Default for NetworkConfig {
    fn default() -> Self {
        Self {
            interfaces: heapless::Vec::new(),
            routes: heapless::Vec::new(),
            globals: NetworkGlobals::default(),
        }
    }
}

impl Default for NetworkGlobals {
    fn default() -> Self {
        Self {
            ipv6: true,
            ip_forward: false,
            ipv6_forward: false,
            default_gateway: None,
            default_gateway_v6: None,
        }
    }
}

impl Default for SystemSettings {
    fn default() -> Self {
        Self {
            hostname: heapless::String::try_from("OpenWrt").unwrap(),
            timezone: heapless::String::try_from("UTC").unwrap(),
            log_level: LogLevel::Info,
            log_destination: LogDestination::Syslog,
            ntp_servers: heapless::Vec::new(),
            ntp_enabled: true,
        }
    }
}

impl Default for WirelessConfig {
    fn default() -> Self {
        Self {
            devices: heapless::Vec::new(),
            interfaces: heapless::Vec::new(),
        }
    }
}

impl Default for FirewallConfig {
    fn default() -> Self {
        Self {
            zones: heapless::Vec::new(),
            rules: heapless::Vec::new(),
            forwards: heapless::Vec::new(),
        }
    }
}

impl Default for DhcpConfig {
    fn default() -> Self {
        Self {
            pools: heapless::Vec::new(),
            static_leases: heapless::Vec::new(),
            globals: DhcpGlobals::default(),
        }
    }
}

impl Default for DhcpGlobals {
    fn default() -> Self {
        Self {
            enabled: true,
            authoritative: false,
            boot_file: None,
            tftp_server: None,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_system_config_serialization() {
        let config = SystemConfig::default();
        let json = serde_json::to_string(&config).unwrap();
        let deserialized: SystemConfig = serde_json::from_str(&json).unwrap();
        assert_eq!(config, deserialized);
    }

    #[test]
    fn test_interface_config() {
        let interface = InterfaceConfig {
            name: heapless::String::try_from("eth0").unwrap(),
            interface_type: InterfaceType::Ethernet,
            protocol: ProtocolConfig::Static {
                address: heapless::String::try_from("***********/24").unwrap(),
                netmask: heapless::String::try_from("*************").unwrap(),
                gateway: Some(heapless::String::try_from("*************").unwrap()),
                dns: heapless::Vec::new(),
            },
            device: Some(heapless::String::try_from("eth0").unwrap()),
            mac_address: None,
            mtu: Some(1500),
            enabled: true,
            metric: None,
            vlan: None,
            bridge: None,
        };

        let json = serde_json::to_string(&interface).unwrap();
        let deserialized: InterfaceConfig = serde_json::from_str(&json).unwrap();
        assert_eq!(interface, deserialized);
    }
}
