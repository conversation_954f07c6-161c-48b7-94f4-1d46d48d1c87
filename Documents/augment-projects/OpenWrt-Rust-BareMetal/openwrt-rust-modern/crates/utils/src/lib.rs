//! OpenWrt Utilities
//!
//! This crate provides modern Rust implementations of core utilities that replace
//! the traditional libubox functionality. It includes:
//!
//! - Modern event loop system (replacing uloop)
//! - Type-safe data structures (lists, trees, hash maps)
//! - JSON handling with strong typing
//! - Blob message format for efficient serialization
//! - Safe string and memory utilities
//! - Async/await support for I/O operations
//!
//! # Features
//!
//! - **Memory Safety**: All operations are memory-safe by design
//! - **Type Safety**: Strong typing prevents common errors
//! - **Performance**: Zero-cost abstractions and efficient algorithms
//! - **Async Support**: Modern async/await patterns for I/O
//! - **C Compatibility**: FFI layer for gradual migration
//!
//! # Example
//!
//! ```rust
//! use openwrt_utils::{EventLoop, JsonValue};
//!
//! let mut event_loop = EventLoop::new();
//! event_loop.start().await?;
//!
//! let json = JsonValue::from_str(r#"{"key": "value"}"#)?;
//! println!("Value: {}", json["key"]);
//! ```

#![cfg_attr(not(feature = "std"), no_std)]
#![deny(missing_docs)]

extern crate alloc;

pub mod event_loop;
pub mod data_structures;
pub mod json;
pub mod blob;
pub mod string_utils;
pub mod memory;

#[cfg(feature = "c-compat")]
pub mod ffi;

// Re-export commonly used types
pub use event_loop::{EventLoop, EventLoopHandle};
pub use data_structures::{List, AvlTree, HashMap};
pub use json::{JsonValue, JsonError};
pub use blob::{BlobMessage, BlobAttr};
pub use openwrt_core::{Error, Result};

/// Utility library version
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_version() {
        assert!(!VERSION.is_empty());
    }
}
