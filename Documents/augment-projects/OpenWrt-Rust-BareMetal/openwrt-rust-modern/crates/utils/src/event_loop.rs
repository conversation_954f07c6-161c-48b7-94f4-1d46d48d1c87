//! Modern event loop implementation
//!
//! This module provides a modern, async-based event loop that replaces the traditional
//! uloop system. It supports both callback-based and async/await patterns.

use openwrt_core::{Error, Result, Priority};
use serde::{Deserialize, Serialize};
use heapless::{Vec as HeaplessVec, FnvIndexMap};

#[cfg(feature = "async")]
use tokio::sync::{mpsc, oneshot};

/// Event loop handle for managing the event system
pub struct EventLoop {
    /// Event handlers
    handlers: FnvIndexMap<u64, Box<dyn EventHandler>, 64>,
    /// Next handler ID
    next_handler_id: u64,
    /// Running state
    running: bool,
    /// Event queue
    event_queue: HeaplessVec<Event, 256>,
    
    #[cfg(feature = "async")]
    /// Async runtime handle
    runtime: Option<tokio::runtime::Handle>,
    
    #[cfg(feature = "async")]
    /// Event sender
    event_sender: Option<mpsc::UnboundedSender<Event>>,
    
    #[cfg(feature = "async")]
    /// Event receiver
    event_receiver: Option<mpsc::UnboundedReceiver<Event>>,
}

/// Event types supported by the event loop
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum Event {
    /// Timer event
    Timer {
        /// Timer ID
        id: u64,
        /// Timer data
        data: u64,
    },
    /// File descriptor event
    Fd {
        /// File descriptor
        fd: i32,
        /// Event flags
        flags: FdEventFlags,
    },
    /// Signal event
    Signal {
        /// Signal number
        signal: i32,
    },
    /// Process event
    Process {
        /// Process ID
        pid: u32,
        /// Exit status
        status: i32,
    },
    /// Custom event
    Custom {
        /// Event type
        event_type: heapless::String<64>,
        /// Event data
        data: heapless::Vec<u8, 256>,
    },
}

/// File descriptor event flags
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub struct FdEventFlags {
    /// Readable
    pub readable: bool,
    /// Writable
    pub writable: bool,
    /// Error condition
    pub error: bool,
    /// Hang up
    pub hangup: bool,
}

impl FdEventFlags {
    /// Create new flags with readable set
    pub fn readable() -> Self {
        Self {
            readable: true,
            writable: false,
            error: false,
            hangup: false,
        }
    }

    /// Create new flags with writable set
    pub fn writable() -> Self {
        Self {
            readable: false,
            writable: true,
            error: false,
            hangup: false,
        }
    }

    /// Create new flags with both readable and writable set
    pub fn read_write() -> Self {
        Self {
            readable: true,
            writable: true,
            error: false,
            hangup: false,
        }
    }
}

/// Event handler trait
pub trait EventHandler: Send + Sync {
    /// Handle an event
    fn handle(&mut self, event: &Event) -> Result<()>;

    /// Get handler priority
    fn priority(&self) -> Priority {
        Priority::Normal
    }

    /// Check if handler can process this event type
    fn can_handle(&self, event: &Event) -> bool;
}

/// Timer configuration
#[derive(Debug, Clone, PartialEq, Eq)]
pub struct TimerConfig {
    /// Timer interval in milliseconds
    pub interval: u64,
    /// Whether timer repeats
    pub repeating: bool,
    /// Timer data
    pub data: u64,
}

/// File descriptor configuration
#[derive(Debug, Clone, PartialEq, Eq)]
pub struct FdConfig {
    /// File descriptor
    pub fd: i32,
    /// Event flags to monitor
    pub flags: FdEventFlags,
}

impl EventLoop {
    /// Create a new event loop
    pub fn new() -> Self {
        #[cfg(feature = "async")]
        let (event_sender, event_receiver) = mpsc::unbounded_channel();

        Self {
            handlers: FnvIndexMap::new(),
            next_handler_id: 1,
            running: false,
            event_queue: HeaplessVec::new(),
            
            #[cfg(feature = "async")]
            runtime: tokio::runtime::Handle::try_current().ok(),
            
            #[cfg(feature = "async")]
            event_sender: Some(event_sender),
            
            #[cfg(feature = "async")]
            event_receiver: Some(event_receiver),
        }
    }

    /// Start the event loop
    pub fn start(&mut self) -> Result<()> {
        self.running = true;
        log::info!("Event loop started");
        Ok(())
    }

    /// Stop the event loop
    pub fn stop(&mut self) -> Result<()> {
        self.running = false;
        log::info!("Event loop stopped");
        Ok(())
    }

    /// Check if event loop is running
    pub fn is_running(&self) -> bool {
        self.running
    }

    /// Register an event handler
    pub fn register_handler(&mut self, handler: Box<dyn EventHandler>) -> Result<u64> {
        let handler_id = self.next_handler_id;
        self.next_handler_id += 1;

        self.handlers.insert(handler_id, handler).map_err(|_| {
            Error::Resource(openwrt_core::error::ResourceError::Exhausted(
                "Handler table full".to_string()
            ))
        })?;

        Ok(handler_id)
    }

    /// Unregister an event handler
    pub fn unregister_handler(&mut self, handler_id: u64) -> Result<()> {
        self.handlers.remove(&handler_id);
        Ok(())
    }

    /// Post an event to the queue
    pub fn post_event(&mut self, event: Event) -> Result<()> {
        #[cfg(feature = "async")]
        if let Some(sender) = &self.event_sender {
            sender.send(event).map_err(|_| {
                Error::Internal("Failed to send event".to_string())
            })?;
            return Ok(());
        }

        self.event_queue.push(event).map_err(|_| {
            Error::Resource(openwrt_core::error::ResourceError::Exhausted(
                "Event queue full".to_string()
            ))
        })
    }

    /// Process one event from the queue
    pub fn process_one(&mut self) -> Result<bool> {
        #[cfg(feature = "async")]
        if let Some(receiver) = &mut self.event_receiver {
            if let Ok(event) = receiver.try_recv() {
                self.dispatch_event(&event)?;
                return Ok(true);
            }
        }

        if let Some(event) = self.event_queue.pop() {
            self.dispatch_event(&event)?;
            Ok(true)
        } else {
            Ok(false)
        }
    }

    /// Process all events in the queue
    pub fn process_all(&mut self) -> Result<usize> {
        let mut processed = 0;
        while self.process_one()? {
            processed += 1;
        }
        Ok(processed)
    }

    /// Run the event loop (blocking)
    pub fn run(&mut self) -> Result<()> {
        self.start()?;
        
        while self.running {
            if !self.process_one()? {
                // No events to process, yield CPU
                #[cfg(feature = "std")]
                std::thread::sleep(std::time::Duration::from_millis(1));
            }
        }
        
        Ok(())
    }

    /// Dispatch an event to appropriate handlers
    fn dispatch_event(&mut self, event: &Event) -> Result<()> {
        // Collect matching handlers
        let mut matching_handlers: HeaplessVec<u64, 32> = HeaplessVec::new();
        
        for (&handler_id, handler) in &self.handlers {
            if handler.can_handle(event) {
                matching_handlers.push(handler_id).map_err(|_| {
                    Error::Internal("Too many matching handlers".to_string())
                })?;
            }
        }

        // Sort by priority
        matching_handlers.sort_by(|&a, &b| {
            let priority_a = self.handlers.get(&a).map(|h| h.priority()).unwrap_or(Priority::Normal);
            let priority_b = self.handlers.get(&b).map(|h| h.priority()).unwrap_or(Priority::Normal);
            priority_b.cmp(&priority_a) // Higher priority first
        });

        // Dispatch to handlers
        for handler_id in matching_handlers {
            if let Some(handler) = self.handlers.get_mut(&handler_id) {
                if let Err(e) = handler.handle(event) {
                    log::error!("Handler {} failed to process event: {}", handler_id, e);
                }
            }
        }

        Ok(())
    }

    /// Add a timer
    pub fn add_timer(&mut self, config: TimerConfig) -> Result<u64> {
        // In a real implementation, this would set up a system timer
        // For now, we'll just return a timer ID
        let timer_id = self.next_handler_id;
        self.next_handler_id += 1;
        
        log::debug!("Added timer {} with interval {}ms", timer_id, config.interval);
        Ok(timer_id)
    }

    /// Remove a timer
    pub fn remove_timer(&mut self, timer_id: u64) -> Result<()> {
        // In a real implementation, this would cancel the system timer
        log::debug!("Removed timer {}", timer_id);
        Ok(())
    }

    /// Add file descriptor monitoring
    pub fn add_fd(&mut self, config: FdConfig) -> Result<u64> {
        // In a real implementation, this would set up epoll/kqueue monitoring
        let fd_id = self.next_handler_id;
        self.next_handler_id += 1;
        
        log::debug!("Added FD monitoring for fd {} with flags {:?}", config.fd, config.flags);
        Ok(fd_id)
    }

    /// Remove file descriptor monitoring
    pub fn remove_fd(&mut self, fd_id: u64) -> Result<()> {
        // In a real implementation, this would remove epoll/kqueue monitoring
        log::debug!("Removed FD monitoring {}", fd_id);
        Ok(())
    }
}

impl Default for EventLoop {
    fn default() -> Self {
        Self::new()
    }
}

/// Event loop handle for async operations
#[cfg(feature = "async")]
pub struct EventLoopHandle {
    /// Event sender
    event_sender: mpsc::UnboundedSender<Event>,
}

#[cfg(feature = "async")]
impl EventLoopHandle {
    /// Create a new handle from an event loop
    pub fn new(event_loop: &EventLoop) -> Option<Self> {
        event_loop.event_sender.as_ref().map(|sender| Self {
            event_sender: sender.clone(),
        })
    }

    /// Post an event asynchronously
    pub async fn post_event(&self, event: Event) -> Result<()> {
        self.event_sender.send(event).map_err(|_| {
            Error::Internal("Failed to send event".to_string())
        })
    }

    /// Post an event and wait for completion
    pub async fn post_event_and_wait(&self, event: Event) -> Result<()> {
        let (tx, rx) = oneshot::channel();
        
        // In a real implementation, we would modify the event to include the response channel
        self.post_event(event).await?;
        
        // Wait for completion (simplified for this example)
        tokio::time::sleep(tokio::time::Duration::from_millis(1)).await;
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    struct TestHandler {
        events_handled: usize,
    }

    impl TestHandler {
        fn new() -> Self {
            Self { events_handled: 0 }
        }
    }

    impl EventHandler for TestHandler {
        fn handle(&mut self, _event: &Event) -> Result<()> {
            self.events_handled += 1;
            Ok(())
        }

        fn can_handle(&self, _event: &Event) -> bool {
            true
        }
    }

    #[test]
    fn test_event_loop_creation() {
        let event_loop = EventLoop::new();
        assert!(!event_loop.is_running());
    }

    #[test]
    fn test_event_loop_start_stop() {
        let mut event_loop = EventLoop::new();
        
        event_loop.start().unwrap();
        assert!(event_loop.is_running());
        
        event_loop.stop().unwrap();
        assert!(!event_loop.is_running());
    }

    #[test]
    fn test_handler_registration() {
        let mut event_loop = EventLoop::new();
        let handler = Box::new(TestHandler::new());
        
        let handler_id = event_loop.register_handler(handler).unwrap();
        assert!(handler_id > 0);
        
        event_loop.unregister_handler(handler_id).unwrap();
    }

    #[test]
    fn test_event_processing() {
        let mut event_loop = EventLoop::new();
        let handler = Box::new(TestHandler::new());
        
        event_loop.register_handler(handler).unwrap();
        
        let event = Event::Timer { id: 1, data: 42 };
        event_loop.post_event(event).unwrap();
        
        let processed = event_loop.process_all().unwrap();
        assert_eq!(processed, 1);
    }

    #[test]
    fn test_timer_operations() {
        let mut event_loop = EventLoop::new();
        
        let timer_config = TimerConfig {
            interval: 1000,
            repeating: false,
            data: 42,
        };
        
        let timer_id = event_loop.add_timer(timer_config).unwrap();
        assert!(timer_id > 0);
        
        event_loop.remove_timer(timer_id).unwrap();
    }

    #[test]
    fn test_fd_operations() {
        let mut event_loop = EventLoop::new();
        
        let fd_config = FdConfig {
            fd: 0, // stdin
            flags: FdEventFlags::readable(),
        };
        
        let fd_id = event_loop.add_fd(fd_config).unwrap();
        assert!(fd_id > 0);
        
        event_loop.remove_fd(fd_id).unwrap();
    }

    #[test]
    fn test_fd_event_flags() {
        let flags = FdEventFlags::readable();
        assert!(flags.readable);
        assert!(!flags.writable);
        
        let flags = FdEventFlags::writable();
        assert!(!flags.readable);
        assert!(flags.writable);
        
        let flags = FdEventFlags::read_write();
        assert!(flags.readable);
        assert!(flags.writable);
    }
}
