//! Modern data structures replacing libubox collections
//!
//! This module provides type-safe, efficient data structures that replace
//! the traditional libubox collections with modern Rust implementations.

use openwrt_core::{Error, Result};
use serde::{Deserialize, Serialize};
use heapless::{Vec as HeaplessVec, FnvIndexMap};

/// Generic list implementation
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct List<T, const N: usize> {
    items: HeaplessVec<T, N>,
}

impl<T, const N: usize> List<T, N> {
    /// Create a new empty list
    pub fn new() -> Self {
        Self {
            items: HeaplessVec::new(),
        }
    }

    /// Add an item to the end of the list
    pub fn push(&mut self, item: T) -> Result<()> {
        self.items.push(item).map_err(|_| {
            Error::Resource(openwrt_core::error::ResourceError::Exhausted(
                "List capacity exceeded".to_string()
            ))
        })
    }

    /// Remove and return the last item
    pub fn pop(&mut self) -> Option<T> {
        self.items.pop()
    }

    /// Insert an item at the specified index
    pub fn insert(&mut self, index: usize, item: T) -> Result<()> {
        if index > self.items.len() {
            return Err(Error::InvalidInput("Index out of bounds".to_string()));
        }
        
        self.items.insert(index, item).map_err(|_| {
            Error::Resource(openwrt_core::error::ResourceError::Exhausted(
                "List capacity exceeded".to_string()
            ))
        })
    }

    /// Remove an item at the specified index
    pub fn remove(&mut self, index: usize) -> Result<T> {
        if index >= self.items.len() {
            return Err(Error::InvalidInput("Index out of bounds".to_string()));
        }
        
        Ok(self.items.remove(index))
    }

    /// Get an item by index
    pub fn get(&self, index: usize) -> Option<&T> {
        self.items.get(index)
    }

    /// Get a mutable reference to an item by index
    pub fn get_mut(&mut self, index: usize) -> Option<&mut T> {
        self.items.get_mut(index)
    }

    /// Get the number of items in the list
    pub fn len(&self) -> usize {
        self.items.len()
    }

    /// Check if the list is empty
    pub fn is_empty(&self) -> bool {
        self.items.is_empty()
    }

    /// Clear all items from the list
    pub fn clear(&mut self) {
        self.items.clear();
    }

    /// Get an iterator over the items
    pub fn iter(&self) -> impl Iterator<Item = &T> {
        self.items.iter()
    }

    /// Get a mutable iterator over the items
    pub fn iter_mut(&mut self) -> impl Iterator<Item = &mut T> {
        self.items.iter_mut()
    }

    /// Find the first item matching a predicate
    pub fn find<F>(&self, predicate: F) -> Option<&T>
    where
        F: Fn(&T) -> bool,
    {
        self.items.iter().find(|item| predicate(item))
    }

    /// Find the index of the first item matching a predicate
    pub fn find_index<F>(&self, predicate: F) -> Option<usize>
    where
        F: Fn(&T) -> bool,
    {
        self.items.iter().position(|item| predicate(item))
    }
}

impl<T, const N: usize> Default for List<T, N> {
    fn default() -> Self {
        Self::new()
    }
}

/// AVL tree node
#[derive(Debug, Clone, PartialEq, Eq)]
struct AvlNode<K, V> {
    key: K,
    value: V,
    height: i8,
    left: Option<Box<AvlNode<K, V>>>,
    right: Option<Box<AvlNode<K, V>>>,
}

impl<K, V> AvlNode<K, V>
where
    K: Ord,
{
    fn new(key: K, value: V) -> Self {
        Self {
            key,
            value,
            height: 1,
            left: None,
            right: None,
        }
    }

    fn update_height(&mut self) {
        let left_height = self.left.as_ref().map_or(0, |n| n.height);
        let right_height = self.right.as_ref().map_or(0, |n| n.height);
        self.height = 1 + left_height.max(right_height);
    }

    fn balance_factor(&self) -> i8 {
        let left_height = self.left.as_ref().map_or(0, |n| n.height);
        let right_height = self.right.as_ref().map_or(0, |n| n.height);
        left_height - right_height
    }
}

/// AVL tree implementation for ordered key-value storage
#[derive(Debug, Clone, PartialEq, Eq)]
pub struct AvlTree<K, V> {
    root: Option<Box<AvlNode<K, V>>>,
    size: usize,
}

impl<K, V> AvlTree<K, V>
where
    K: Ord + Clone,
    V: Clone,
{
    /// Create a new empty AVL tree
    pub fn new() -> Self {
        Self {
            root: None,
            size: 0,
        }
    }

    /// Insert a key-value pair
    pub fn insert(&mut self, key: K, value: V) -> Option<V> {
        let (new_root, old_value) = Self::insert_node(self.root.take(), key, value);
        self.root = new_root;
        if old_value.is_none() {
            self.size += 1;
        }
        old_value
    }

    /// Get a value by key
    pub fn get(&self, key: &K) -> Option<&V> {
        Self::get_node(self.root.as_ref(), key)
    }

    /// Remove a key-value pair
    pub fn remove(&mut self, key: &K) -> Option<V> {
        let (new_root, removed_value) = Self::remove_node(self.root.take(), key);
        self.root = new_root;
        if removed_value.is_some() {
            self.size -= 1;
        }
        removed_value
    }

    /// Check if the tree contains a key
    pub fn contains_key(&self, key: &K) -> bool {
        self.get(key).is_some()
    }

    /// Get the number of key-value pairs
    pub fn len(&self) -> usize {
        self.size
    }

    /// Check if the tree is empty
    pub fn is_empty(&self) -> bool {
        self.size == 0
    }

    /// Clear all key-value pairs
    pub fn clear(&mut self) {
        self.root = None;
        self.size = 0;
    }

    fn insert_node(
        node: Option<Box<AvlNode<K, V>>>,
        key: K,
        value: V,
    ) -> (Option<Box<AvlNode<K, V>>>, Option<V>) {
        match node {
            None => (Some(Box::new(AvlNode::new(key, value))), None),
            Some(mut node) => {
                let old_value = match key.cmp(&node.key) {
                    core::cmp::Ordering::Less => {
                        let (new_left, old_val) = Self::insert_node(node.left.take(), key, value);
                        node.left = new_left;
                        old_val
                    }
                    core::cmp::Ordering::Greater => {
                        let (new_right, old_val) = Self::insert_node(node.right.take(), key, value);
                        node.right = new_right;
                        old_val
                    }
                    core::cmp::Ordering::Equal => {
                        let old_val = core::mem::replace(&mut node.value, value);
                        Some(old_val)
                    }
                };

                node.update_height();
                (Some(Self::balance(node)), old_value)
            }
        }
    }

    fn get_node(node: Option<&Box<AvlNode<K, V>>>, key: &K) -> Option<&V> {
        match node {
            None => None,
            Some(node) => match key.cmp(&node.key) {
                core::cmp::Ordering::Less => Self::get_node(node.left.as_ref(), key),
                core::cmp::Ordering::Greater => Self::get_node(node.right.as_ref(), key),
                core::cmp::Ordering::Equal => Some(&node.value),
            },
        }
    }

    fn remove_node(
        node: Option<Box<AvlNode<K, V>>>,
        key: &K,
    ) -> (Option<Box<AvlNode<K, V>>>, Option<V>) {
        match node {
            None => (None, None),
            Some(mut node) => match key.cmp(&node.key) {
                core::cmp::Ordering::Less => {
                    let (new_left, removed_value) = Self::remove_node(node.left.take(), key);
                    node.left = new_left;
                    node.update_height();
                    (Some(Self::balance(node)), removed_value)
                }
                core::cmp::Ordering::Greater => {
                    let (new_right, removed_value) = Self::remove_node(node.right.take(), key);
                    node.right = new_right;
                    node.update_height();
                    (Some(Self::balance(node)), removed_value)
                }
                core::cmp::Ordering::Equal => {
                    let removed_value = node.value.clone();
                    match (node.left.take(), node.right.take()) {
                        (None, None) => (None, Some(removed_value)),
                        (Some(left), None) => (Some(left), Some(removed_value)),
                        (None, Some(right)) => (Some(right), Some(removed_value)),
                        (Some(left), Some(right)) => {
                            // Find the minimum node in the right subtree
                            let (new_right, min_node) = Self::remove_min(right);
                            let mut replacement = min_node;
                            replacement.left = Some(left);
                            replacement.right = new_right;
                            replacement.update_height();
                            (Some(Self::balance(replacement)), Some(removed_value))
                        }
                    }
                }
            },
        }
    }

    fn remove_min(mut node: Box<AvlNode<K, V>>) -> (Option<Box<AvlNode<K, V>>>, Box<AvlNode<K, V>>) {
        match node.left.take() {
            None => (node.right.take(), node),
            Some(left) => {
                let (new_left, min_node) = Self::remove_min(left);
                node.left = new_left;
                node.update_height();
                (Some(Self::balance(node)), min_node)
            }
        }
    }

    fn balance(mut node: Box<AvlNode<K, V>>) -> Box<AvlNode<K, V>> {
        let balance_factor = node.balance_factor();

        if balance_factor > 1 {
            // Left heavy
            if let Some(ref left) = node.left {
                if left.balance_factor() < 0 {
                    // Left-Right case
                    node.left = Some(Self::rotate_left(node.left.take().unwrap()));
                }
            }
            // Left-Left case
            Self::rotate_right(node)
        } else if balance_factor < -1 {
            // Right heavy
            if let Some(ref right) = node.right {
                if right.balance_factor() > 0 {
                    // Right-Left case
                    node.right = Some(Self::rotate_right(node.right.take().unwrap()));
                }
            }
            // Right-Right case
            Self::rotate_left(node)
        } else {
            node
        }
    }

    fn rotate_left(mut node: Box<AvlNode<K, V>>) -> Box<AvlNode<K, V>> {
        let mut new_root = node.right.take().unwrap();
        node.right = new_root.left.take();
        node.update_height();
        new_root.left = Some(node);
        new_root.update_height();
        new_root
    }

    fn rotate_right(mut node: Box<AvlNode<K, V>>) -> Box<AvlNode<K, V>> {
        let mut new_root = node.left.take().unwrap();
        node.left = new_root.right.take();
        node.update_height();
        new_root.right = Some(node);
        new_root.update_height();
        new_root
    }
}

impl<K, V> Default for AvlTree<K, V>
where
    K: Ord + Clone,
    V: Clone,
{
    fn default() -> Self {
        Self::new()
    }
}

/// Hash map implementation using heapless
pub type HashMap<K, V, const N: usize> = FnvIndexMap<K, V, N>;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_list_operations() {
        let mut list: List<i32, 10> = List::new();
        assert!(list.is_empty());
        assert_eq!(list.len(), 0);

        list.push(1).unwrap();
        list.push(2).unwrap();
        list.push(3).unwrap();

        assert_eq!(list.len(), 3);
        assert_eq!(list.get(0), Some(&1));
        assert_eq!(list.get(1), Some(&2));
        assert_eq!(list.get(2), Some(&3));

        assert_eq!(list.pop(), Some(3));
        assert_eq!(list.len(), 2);

        list.insert(1, 10).unwrap();
        assert_eq!(list.get(1), Some(&10));
        assert_eq!(list.len(), 3);

        assert_eq!(list.remove(1).unwrap(), 10);
        assert_eq!(list.len(), 2);
    }

    #[test]
    fn test_list_find() {
        let mut list: List<i32, 10> = List::new();
        list.push(1).unwrap();
        list.push(2).unwrap();
        list.push(3).unwrap();

        assert_eq!(list.find(|&x| x == 2), Some(&2));
        assert_eq!(list.find(|&x| x == 5), None);

        assert_eq!(list.find_index(|&x| x == 2), Some(1));
        assert_eq!(list.find_index(|&x| x == 5), None);
    }

    #[test]
    fn test_avl_tree_operations() {
        let mut tree: AvlTree<i32, String> = AvlTree::new();
        assert!(tree.is_empty());
        assert_eq!(tree.len(), 0);

        tree.insert(1, "one".to_string());
        tree.insert(2, "two".to_string());
        tree.insert(3, "three".to_string());

        assert_eq!(tree.len(), 3);
        assert_eq!(tree.get(&1), Some(&"one".to_string()));
        assert_eq!(tree.get(&2), Some(&"two".to_string()));
        assert_eq!(tree.get(&3), Some(&"three".to_string()));
        assert_eq!(tree.get(&4), None);

        assert!(tree.contains_key(&1));
        assert!(!tree.contains_key(&4));

        assert_eq!(tree.remove(&2), Some("two".to_string()));
        assert_eq!(tree.len(), 2);
        assert_eq!(tree.get(&2), None);

        tree.clear();
        assert!(tree.is_empty());
        assert_eq!(tree.len(), 0);
    }

    #[test]
    fn test_avl_tree_balance() {
        let mut tree: AvlTree<i32, i32> = AvlTree::new();
        
        // Insert in ascending order to test balancing
        for i in 1..=7 {
            tree.insert(i, i * 10);
        }

        assert_eq!(tree.len(), 7);
        
        // Verify all values are accessible
        for i in 1..=7 {
            assert_eq!(tree.get(&i), Some(&(i * 10)));
        }
    }
}
