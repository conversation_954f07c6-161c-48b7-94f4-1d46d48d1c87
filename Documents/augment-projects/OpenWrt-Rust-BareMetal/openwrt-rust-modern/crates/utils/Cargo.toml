[package]
name = "openwrt-utils"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
description = "Modern utility library replacing libubox functionality"
keywords.workspace = true
categories.workspace = true

[dependencies]
# Core dependencies
openwrt-core = { path = "../core" }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
postcard = { workspace = true }
thiserror = { workspace = true }
log = { workspace = true }
heapless = { workspace = true }
bitflags = { workspace = true }

# Async support
tokio = { workspace = true, features = ["rt", "net", "time", "macros", "sync"], optional = true }
futures = { workspace = true, optional = true }

# Data structures
linked_list_allocator = { workspace = true, optional = true }

# FFI support for C compatibility
libc = { version = "0.2", optional = true }

[dev-dependencies]
tokio = { workspace = true, features = ["rt", "macros", "time", "test-util"] }
proptest = { workspace = true }
criterion = { workspace = true }

[features]
default = ["std", "async"]
std = ["tokio", "futures", "libc"]
async = ["tokio", "futures"]
embedded = ["linked_list_allocator"]
c-compat = ["libc"]

[[bench]]
name = "utils_benchmarks"
harness = false
