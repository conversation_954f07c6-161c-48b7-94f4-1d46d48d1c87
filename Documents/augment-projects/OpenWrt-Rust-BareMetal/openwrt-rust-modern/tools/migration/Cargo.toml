[package]
name = "openwrt-migration"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
description = "Migration tool for OpenWrt legacy to modern Rust components"
keywords.workspace = true
categories.workspace = true

[[bin]]
name = "openwrt-migrate"
path = "src/main.rs"

[dependencies]
# Core dependencies
openwrt-core = { path = "../../crates/core" }
openwrt-config = { path = "../../crates/config" }
openwrt-compat = { path = "../../crates/compat" }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
toml = { workspace = true }
thiserror = { workspace = true }
anyhow = { workspace = true }
log = { workspace = true }
env_logger = { workspace = true }

# CLI framework
clap = { version = "4.4", features = ["derive", "cargo"] }

# File system operations
walkdir = "2.4"
glob = "0.3"
tempfile = "3.8"

# Process execution
tokio = { workspace = true, features = ["rt", "process", "macros", "fs", "io-util"] }

# Configuration parsing
nom = { workspace = true }

# Progress reporting
indicatif = "0.17"

# Backup and versioning
chrono = { version = "0.4", features = ["serde"] }

[dev-dependencies]
tempfile = "3.8"
assert_cmd = "2.0"
predicates = "3.0"
