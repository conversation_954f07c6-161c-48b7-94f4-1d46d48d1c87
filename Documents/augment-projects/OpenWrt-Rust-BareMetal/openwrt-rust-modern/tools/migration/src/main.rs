//! OpenWrt Migration Tool
//!
//! This tool provides comprehensive migration capabilities for transitioning from
//! legacy OpenWrt C-based components to modern Rust implementations while
//! preserving all functionality and configuration.

use anyhow::{Context, Result};
use clap::{Parser, Subcommand};
use log::{info, warn, error};
use std::path::PathBuf;
use indicatif::{ProgressBar, ProgressStyle};

mod analyzer;
mod migrator;
mod validator;
mod backup;
mod reporter;

use analyzer::LegacyAnalyzer;
use migrator::ConfigMigrator;
use validator::MigrationValidator;
use backup::BackupManager;
use reporter::MigrationReporter;

/// OpenWrt Migration Tool
#[derive(Parser)]
#[command(name = "openwrt-migrate")]
#[command(about = "Migration tool for OpenWrt legacy to modern Rust components")]
#[command(version)]
struct Cli {
    /// Verbose output
    #[arg(short, long)]
    verbose: bool,

    /// Dry run (don't make actual changes)
    #[arg(short, long)]
    dry_run: bool,

    /// Source directory (legacy OpenWrt)
    #[arg(short, long, default_value = "/etc/config")]
    source: PathBuf,

    /// Target directory (modern Rust config)
    #[arg(short, long, default_value = "/etc/openwrt-rust")]
    target: PathBuf,

    /// Backup directory
    #[arg(short, long, default_value = "/var/backups/openwrt-migration")]
    backup: PathBuf,

    #[command(subcommand)]
    command: Commands,
}

#[derive(Subcommand)]
enum Commands {
    /// Analyze legacy configuration
    Analyze {
        /// Generate detailed report
        #[arg(long)]
        detailed: bool,
        /// Output format (json/yaml/text)
        #[arg(long, default_value = "text")]
        format: String,
        /// Output file
        #[arg(short, long)]
        output: Option<PathBuf>,
    },
    /// Migrate configuration files
    Migrate {
        /// Configuration type to migrate
        config_type: Option<String>,
        /// Force migration even if target exists
        #[arg(long)]
        force: bool,
        /// Skip validation
        #[arg(long)]
        skip_validation: bool,
    },
    /// Validate migrated configuration
    Validate {
        /// Configuration file to validate
        config: Option<PathBuf>,
        /// Strict validation mode
        #[arg(long)]
        strict: bool,
    },
    /// Create backup of current configuration
    Backup {
        /// Backup name/tag
        #[arg(short, long)]
        name: Option<String>,
        /// Include system state
        #[arg(long)]
        include_state: bool,
    },
    /// Restore from backup
    Restore {
        /// Backup to restore from
        backup_id: String,
        /// Restore target (legacy/modern)
        #[arg(long, default_value = "legacy")]
        target: String,
    },
    /// Generate migration plan
    Plan {
        /// Output file for migration plan
        #[arg(short, long, default_value = "migration-plan.json")]
        output: PathBuf,
        /// Include rollback steps
        #[arg(long)]
        include_rollback: bool,
    },
    /// Execute migration plan
    Execute {
        /// Migration plan file
        plan: PathBuf,
        /// Step to start from
        #[arg(long)]
        start_step: Option<usize>,
        /// Step to stop at
        #[arg(long)]
        stop_step: Option<usize>,
    },
    /// Generate migration report
    Report {
        /// Report type (summary/detailed/comparison)
        #[arg(long, default_value = "summary")]
        report_type: String,
        /// Output format (html/pdf/json)
        #[arg(long, default_value = "html")]
        format: String,
        /// Output file
        #[arg(short, long)]
        output: Option<PathBuf>,
    },
    /// Rollback migration
    Rollback {
        /// Rollback to specific step
        #[arg(long)]
        to_step: Option<usize>,
        /// Force rollback without confirmation
        #[arg(long)]
        force: bool,
    },
}

#[tokio::main]
async fn main() -> Result<()> {
    let cli = Cli::parse();

    // Initialize logging
    let log_level = if cli.verbose {
        log::LevelFilter::Debug
    } else {
        log::LevelFilter::Info
    };
    
    env_logger::Builder::from_default_env()
        .filter_level(log_level)
        .init();

    info!("OpenWrt Migration Tool starting");
    info!("Source: {:?}", cli.source);
    info!("Target: {:?}", cli.target);
    info!("Backup: {:?}", cli.backup);

    if cli.dry_run {
        warn!("Running in DRY RUN mode - no changes will be made");
    }

    // Initialize components
    let analyzer = LegacyAnalyzer::new(&cli.source);
    let mut migrator = ConfigMigrator::new(&cli.source, &cli.target);
    let validator = MigrationValidator::new();
    let backup_manager = BackupManager::new(&cli.backup);
    let reporter = MigrationReporter::new();

    migrator.set_dry_run(cli.dry_run);

    // Execute command
    match cli.command {
        Commands::Analyze { detailed, format, output } => {
            info!("Analyzing legacy configuration...");
            
            let progress = ProgressBar::new_spinner();
            progress.set_style(ProgressStyle::default_spinner()
                .template("{spinner:.green} {msg}")
                .unwrap());
            progress.set_message("Scanning configuration files...");

            let analysis = analyzer.analyze_all().await?;
            progress.finish_with_message("Analysis complete");

            let report = if detailed {
                reporter.generate_detailed_analysis(&analysis, &format)?
            } else {
                reporter.generate_summary_analysis(&analysis, &format)?
            };

            if let Some(output_path) = output {
                std::fs::write(&output_path, report)
                    .with_context(|| format!("Failed to write report to {:?}", output_path))?;
                info!("Analysis report written to {:?}", output_path);
            } else {
                println!("{}", report);
            }
        }

        Commands::Migrate { config_type, force, skip_validation } => {
            info!("Starting configuration migration...");

            // Create backup first
            let backup_id = backup_manager.create_backup("pre-migration", true).await?;
            info!("Created backup: {}", backup_id);

            let progress = ProgressBar::new_spinner();
            progress.set_style(ProgressStyle::default_spinner()
                .template("{spinner:.green} {msg}")
                .unwrap());

            if let Some(config_type) = config_type {
                progress.set_message(format!("Migrating {} configuration...", config_type));
                migrator.migrate_config_type(&config_type, force).await?;
            } else {
                progress.set_message("Migrating all configurations...");
                migrator.migrate_all(force).await?;
            }

            progress.finish_with_message("Migration complete");

            if !skip_validation {
                info!("Validating migrated configuration...");
                let validation_result = validator.validate_all(&cli.target).await?;
                
                if validation_result.is_valid() {
                    info!("Migration validation passed");
                } else {
                    error!("Migration validation failed: {:?}", validation_result.errors());
                    return Err(anyhow::anyhow!("Migration validation failed"));
                }
            }
        }

        Commands::Validate { config, strict } => {
            info!("Validating configuration...");

            let validation_result = if let Some(config_path) = config {
                validator.validate_file(&config_path, strict).await?
            } else {
                validator.validate_all(&cli.target).await?
            };

            if validation_result.is_valid() {
                info!("Validation passed");
            } else {
                error!("Validation failed:");
                for error in validation_result.errors() {
                    error!("  - {}", error);
                }
                return Err(anyhow::anyhow!("Validation failed"));
            }
        }

        Commands::Backup { name, include_state } => {
            let backup_name = name.unwrap_or_else(|| {
                chrono::Utc::now().format("manual-%Y%m%d-%H%M%S").to_string()
            });

            info!("Creating backup: {}", backup_name);
            let backup_id = backup_manager.create_backup(&backup_name, include_state).await?;
            info!("Backup created successfully: {}", backup_id);
        }

        Commands::Restore { backup_id, target } => {
            info!("Restoring from backup: {}", backup_id);
            
            let restore_target = match target.as_str() {
                "legacy" => &cli.source,
                "modern" => &cli.target,
                _ => return Err(anyhow::anyhow!("Invalid restore target: {}", target)),
            };

            backup_manager.restore_backup(&backup_id, restore_target).await?;
            info!("Restore completed successfully");
        }

        Commands::Plan { output, include_rollback } => {
            info!("Generating migration plan...");

            let analysis = analyzer.analyze_all().await?;
            let plan = migrator.generate_migration_plan(&analysis, include_rollback).await?;

            let plan_json = serde_json::to_string_pretty(&plan)?;
            std::fs::write(&output, plan_json)
                .with_context(|| format!("Failed to write plan to {:?}", output))?;

            info!("Migration plan written to {:?}", output);
        }

        Commands::Execute { plan, start_step, stop_step } => {
            info!("Executing migration plan: {:?}", plan);

            let plan_content = std::fs::read_to_string(&plan)
                .with_context(|| format!("Failed to read plan from {:?}", plan))?;
            let migration_plan: serde_json::Value = serde_json::from_str(&plan_content)?;

            migrator.execute_plan(&migration_plan, start_step, stop_step).await?;
            info!("Migration plan executed successfully");
        }

        Commands::Report { report_type, format, output } => {
            info!("Generating migration report...");

            let analysis = analyzer.analyze_all().await?;
            let report = match report_type.as_str() {
                "summary" => reporter.generate_summary_report(&analysis, &format)?,
                "detailed" => reporter.generate_detailed_report(&analysis, &format)?,
                "comparison" => reporter.generate_comparison_report(&cli.source, &cli.target, &format).await?,
                _ => return Err(anyhow::anyhow!("Invalid report type: {}", report_type)),
            };

            if let Some(output_path) = output {
                std::fs::write(&output_path, report)
                    .with_context(|| format!("Failed to write report to {:?}", output_path))?;
                info!("Report written to {:?}", output_path);
            } else {
                println!("{}", report);
            }
        }

        Commands::Rollback { to_step, force } => {
            if !force {
                warn!("Rollback will revert migration changes. Use --force to confirm.");
                return Ok(());
            }

            info!("Rolling back migration...");
            migrator.rollback(to_step).await?;
            info!("Rollback completed successfully");
        }
    }

    info!("OpenWrt Migration Tool completed successfully");
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use assert_cmd::Command;
    use predicates::prelude::*;
    use tempfile::TempDir;

    #[test]
    fn test_cli_help() {
        let mut cmd = Command::cargo_bin("openwrt-migrate").unwrap();
        cmd.arg("--help");
        cmd.assert()
            .success()
            .stdout(predicate::str::contains("OpenWrt Migration Tool"));
    }

    #[test]
    fn test_cli_version() {
        let mut cmd = Command::cargo_bin("openwrt-migrate").unwrap();
        cmd.arg("--version");
        cmd.assert()
            .success()
            .stdout(predicate::str::contains(env!("CARGO_PKG_VERSION")));
    }

    #[tokio::test]
    async fn test_dry_run_migration() {
        let temp_dir = TempDir::new().unwrap();
        let source_dir = temp_dir.path().join("source");
        let target_dir = temp_dir.path().join("target");
        let backup_dir = temp_dir.path().join("backup");

        std::fs::create_dir_all(&source_dir).unwrap();
        std::fs::create_dir_all(&target_dir).unwrap();
        std::fs::create_dir_all(&backup_dir).unwrap();

        // Create a sample legacy config file
        let network_config = r#"
config interface 'lan'
    option ifname 'eth0'
    option proto 'static'
    option ipaddr '***********'
    option netmask '*************'
"#;
        std::fs::write(source_dir.join("network"), network_config).unwrap();

        let analyzer = LegacyAnalyzer::new(&source_dir);
        let analysis = analyzer.analyze_all().await.unwrap();
        
        assert!(!analysis.config_files.is_empty());
        assert!(analysis.config_files.iter().any(|f| f.name == "network"));
    }
}
