//! OpenWrt Build Helper
//!
//! This tool provides comprehensive build system support for OpenWrt Rust components,
//! including cross-compilation, packaging, and integration with the existing OpenWrt
//! build infrastructure.

use anyhow::{Context, Result};
use clap::{Parser, Subcommand};
use log::{info, warn, error};
use std::path::PathBuf;

mod builder;
mod cross_compile;
mod package;
mod integration;
mod config;

use builder::Builder;
use cross_compile::CrossCompiler;
use package::Packager;
use integration::OpenWrtIntegrator;
use config::BuildConfig;

/// OpenWrt Build Helper
#[derive(Parser)]
#[command(name = "openwrt-build")]
#[command(about = "Build system helper for OpenWrt Rust components")]
#[command(version)]
struct Cli {
    /// Verbose output
    #[arg(short, long)]
    verbose: bool,

    /// Configuration file
    #[arg(short, long, default_value = "build.toml")]
    config: PathBuf,

    /// Target architecture
    #[arg(short, long)]
    target: Option<String>,

    /// Build profile (debug/release)
    #[arg(short, long, default_value = "release")]
    profile: String,

    #[command(subcommand)]
    command: Commands,
}

#[derive(Subcommand)]
enum Commands {
    /// Build Rust components
    Build {
        /// Component to build (all if not specified)
        component: Option<String>,
        /// Enable cross-compilation
        #[arg(long)]
        cross: bool,
        /// Additional cargo features
        #[arg(long)]
        features: Vec<String>,
    },
    /// Package components for OpenWrt
    Package {
        /// Component to package
        component: String,
        /// Output directory
        #[arg(short, long, default_value = "packages")]
        output: PathBuf,
        /// Package format (ipk/tar)
        #[arg(long, default_value = "ipk")]
        format: String,
    },
    /// Generate OpenWrt integration files
    Integrate {
        /// Generate Makefiles
        #[arg(long)]
        makefiles: bool,
        /// Generate package definitions
        #[arg(long)]
        packages: bool,
        /// Generate configuration files
        #[arg(long)]
        configs: bool,
    },
    /// Cross-compile for target architecture
    Cross {
        /// Target triple
        target: String,
        /// Component to cross-compile
        component: Option<String>,
    },
    /// Clean build artifacts
    Clean {
        /// Also clean target directory
        #[arg(long)]
        all: bool,
    },
    /// Test components
    Test {
        /// Component to test
        component: Option<String>,
        /// Run integration tests
        #[arg(long)]
        integration: bool,
    },
    /// Generate documentation
    Doc {
        /// Open documentation in browser
        #[arg(long)]
        open: bool,
        /// Include private items
        #[arg(long)]
        private: bool,
    },
    /// Check code quality
    Check {
        /// Run clippy lints
        #[arg(long)]
        clippy: bool,
        /// Run rustfmt
        #[arg(long)]
        fmt: bool,
        /// Check security vulnerabilities
        #[arg(long)]
        audit: bool,
    },
}

#[tokio::main]
async fn main() -> Result<()> {
    let cli = Cli::parse();

    // Initialize logging
    let log_level = if cli.verbose {
        log::LevelFilter::Debug
    } else {
        log::LevelFilter::Info
    };
    
    env_logger::Builder::from_default_env()
        .filter_level(log_level)
        .init();

    info!("OpenWrt Build Helper starting");

    // Load configuration
    let config = BuildConfig::load(&cli.config)
        .with_context(|| format!("Failed to load config from {:?}", cli.config))?;

    info!("Loaded build configuration from {:?}", cli.config);

    // Execute command
    match cli.command {
        Commands::Build { component, cross, features } => {
            let mut builder = Builder::new(config);
            
            if let Some(target) = cli.target {
                builder.set_target(&target);
            }
            
            builder.set_profile(&cli.profile);
            
            if cross {
                builder.enable_cross_compilation();
            }
            
            for feature in features {
                builder.add_feature(&feature);
            }
            
            if let Some(component) = component {
                builder.build_component(&component).await?;
            } else {
                builder.build_all().await?;
            }
        }
        
        Commands::Package { component, output, format } => {
            let packager = Packager::new(config);
            packager.package_component(&component, &output, &format).await?;
        }
        
        Commands::Integrate { makefiles, packages, configs } => {
            let integrator = OpenWrtIntegrator::new(config);
            
            if makefiles {
                integrator.generate_makefiles().await?;
            }
            
            if packages {
                integrator.generate_package_definitions().await?;
            }
            
            if configs {
                integrator.generate_config_files().await?;
            }
            
            if !makefiles && !packages && !configs {
                // Generate all by default
                integrator.generate_all().await?;
            }
        }
        
        Commands::Cross { target, component } => {
            let cross_compiler = CrossCompiler::new(config);
            
            if let Some(component) = component {
                cross_compiler.cross_compile_component(&target, &component).await?;
            } else {
                cross_compiler.cross_compile_all(&target).await?;
            }
        }
        
        Commands::Clean { all } => {
            let builder = Builder::new(config);
            
            if all {
                builder.clean_all().await?;
            } else {
                builder.clean().await?;
            }
        }
        
        Commands::Test { component, integration } => {
            let builder = Builder::new(config);
            
            if integration {
                builder.run_integration_tests(component.as_deref()).await?;
            } else {
                builder.run_tests(component.as_deref()).await?;
            }
        }
        
        Commands::Doc { open, private } => {
            let builder = Builder::new(config);
            builder.generate_docs(open, private).await?;
        }
        
        Commands::Check { clippy, fmt, audit } => {
            let builder = Builder::new(config);
            
            if clippy {
                builder.run_clippy().await?;
            }
            
            if fmt {
                builder.run_rustfmt().await?;
            }
            
            if audit {
                builder.run_audit().await?;
            }
            
            if !clippy && !fmt && !audit {
                // Run all checks by default
                builder.run_all_checks().await?;
            }
        }
    }

    info!("OpenWrt Build Helper completed successfully");
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use assert_cmd::Command;
    use predicates::prelude::*;

    #[test]
    fn test_cli_help() {
        let mut cmd = Command::cargo_bin("openwrt-build").unwrap();
        cmd.arg("--help");
        cmd.assert()
            .success()
            .stdout(predicate::str::contains("OpenWrt Build Helper"));
    }

    #[test]
    fn test_cli_version() {
        let mut cmd = Command::cargo_bin("openwrt-build").unwrap();
        cmd.arg("--version");
        cmd.assert()
            .success()
            .stdout(predicate::str::contains(env!("CARGO_PKG_VERSION")));
    }

    #[tokio::test]
    async fn test_build_config_loading() {
        let temp_dir = tempfile::tempdir().unwrap();
        let config_path = temp_dir.path().join("test_build.toml");
        
        let config_content = r#"
[build]
workspace_root = "."
target_dir = "target"
default_profile = "release"

[targets]
"mips-unknown-linux-musl" = { enabled = true, features = ["embedded"] }
"arm-unknown-linux-musleabihf" = { enabled = true, features = ["embedded"] }
"#;
        
        std::fs::write(&config_path, config_content).unwrap();
        
        let config = BuildConfig::load(&config_path).unwrap();
        assert_eq!(config.build.default_profile, "release");
        assert!(config.targets.contains_key("mips-unknown-linux-musl"));
    }
}
