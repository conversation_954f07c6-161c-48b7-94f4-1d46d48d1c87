[package]
name = "openwrt-build-helper"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
description = "Build system helper for OpenWrt Rust components"
keywords.workspace = true
categories.workspace = true

[[bin]]
name = "openwrt-build"
path = "src/main.rs"

[dependencies]
# Core dependencies
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
toml = { workspace = true }
thiserror = { workspace = true }
anyhow = { workspace = true }
log = { workspace = true }
env_logger = { workspace = true }

# CLI framework
clap = { version = "4.4", features = ["derive", "cargo"] }

# File system operations
walkdir = "2.4"
glob = "0.3"
tempfile = "3.8"

# Process execution
tokio = { workspace = true, features = ["rt", "process", "macros", "fs", "io-util"] }

# Build tools
cc = { workspace = true }
bindgen = { workspace = true }

# Compression
flate2 = "1.0"
tar = "0.4"

# Cross-compilation support
cross = "0.2"

[dev-dependencies]
tempfile = "3.8"
assert_cmd = "2.0"
predicates = "3.0"
