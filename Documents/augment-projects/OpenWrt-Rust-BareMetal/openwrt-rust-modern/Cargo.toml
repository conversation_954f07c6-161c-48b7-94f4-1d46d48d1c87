[workspace]
members = [
    "crates/core",
    "crates/config", 
    "crates/network",
    "crates/utils",
    "crates/ipc",
    "crates/security",
    "crates/drivers",
    "crates/compat",
    "tools/migration",
    "tools/build-helper",
]

resolver = "2"

[workspace.package]
version = "0.1.0"
edition = "2021"
authors = ["OpenWrt Rust Migration Team"]
license = "GPL-2.0"
repository = "https://github.com/openwrt/openwrt-rust"
homepage = "https://openwrt.org"
documentation = "https://docs.rs/openwrt-rust"
keywords = ["openwrt", "embedded", "networking", "router", "firmware"]
categories = ["embedded", "network-programming", "os"]

[workspace.dependencies]
# Core embedded dependencies
spin = { version = "0.9", default-features = false, features = ["mutex", "spin_mutex"] }
linked_list_allocator = { version = "0.10", default-features = false, features = ["use_spin"] }
volatile = { version = "0.4", default-features = false }
bitflags = { version = "2.4", default-features = false }
nb = { version = "1.1", default-features = false }

# Embedded HAL and utilities
embedded-hal = { version = "1.0", default-features = false }
embedded-hal-async = { version = "1.0", default-features = false }
embedded-storage = { version = "0.3", default-features = false }
embedded-io = { version = "0.6", default-features = false }
heapless = { version = "0.8", default-features = false, features = ["serde"] }

# Architecture-specific support
x86_64 = { version = "0.15", default-features = false, features = ["instructions"] }
lazy_static = { version = "1.4", features = ["spin_no_std"], default-features = false }

# Async runtime
tokio = { version = "1.0", default-features = false, features = ["rt", "net", "time", "macros"] }
futures = { version = "0.3", default-features = false, features = ["alloc"] }

# Networking (no_std compatible)
smoltcp = { version = "0.11", default-features = false, features = [
    "proto-ipv4", "proto-ipv6", "proto-igmp",
    "socket-tcp", "socket-udp", "socket-icmp", "socket-dhcpv4", "socket-dns",
    "medium-ethernet", "medium-ip"
] }

# Serialization for configuration
serde = { version = "1.0", default-features = false, features = ["derive"] }
serde_json = { version = "1.0", default-features = false, features = ["alloc"] }
postcard = { version = "1.0", default-features = false, features = ["heapless"] }
toml = { version = "0.8", default-features = false }

# Parser combinator for configuration parsing
nom = { version = "7.1", default-features = false, features = ["alloc"] }

# Error handling
thiserror = { version = "1.0", default-features = false }
anyhow = { version = "1.0", default-features = false }

# Logging
log = { version = "0.4", default-features = false }
env_logger = { version = "0.10", default-features = false }

# FFI support for C integration
bindgen = { version = "0.69" }
cc = { version = "1.0" }

# Cryptography
ring = { version = "0.17", default-features = false }
rustls = { version = "0.22", default-features = false, features = ["ring"] }

# Testing dependencies
criterion = { version = "0.5", default-features = false }
proptest = { version = "1.0", default-features = false }
serial_test = { version = "3.0" }
mockall = { version = "0.12" }

[profile.dev]
panic = "abort"
opt-level = 0
debug = true
lto = false
incremental = true

[profile.release]
panic = "abort"
opt-level = "s"  # Optimize for size
debug = false
lto = true
codegen-units = 1
strip = true

[profile.test]
opt-level = 1
debug = true

[profile.bench]
opt-level = 3
debug = false
lto = true

# Target-specific configurations
[target.'cfg(target_arch = "x86_64")']
rustflags = ["-C", "target-cpu=native"]

[target.'cfg(target_arch = "arm")']
rustflags = ["-C", "target-cpu=cortex-a9"]

[target.'cfg(target_arch = "aarch64")']
rustflags = ["-C", "target-cpu=cortex-a53"]

[target.'cfg(target_arch = "mips")']
rustflags = ["-C", "target-cpu=mips32r2"]
