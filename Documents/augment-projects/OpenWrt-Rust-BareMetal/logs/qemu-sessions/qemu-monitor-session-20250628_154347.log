QEMU 10.0.2 monitor - type 'help' for more information
(qemu) i[K[Din[K[D[Dinf[K[D[D[Dinfo[K[D[D[D[Dinfo [K[D[D[D[D[Dinfo r[K[D[D[D[D[D[Dinfo re[K[D[D[D[D[D[D[Dinfo reg[K[D[D[D[D[D[D[D[Dinfo regi[K[D[D[D[D[D[D[D[D[Dinfo regis[K[D[D[D[D[D[D[D[D[D[Dinfo regist[K[D[D[D[D[D[D[D[D[D[D[Dinfo registe[K[D[D[D[D[D[D[D[D[D[D[D[Dinfo register[K[D[D[D[D[D[D[D[D[D[D[D[D[Dinfo registers[K

CPU#0
EAX=00000000 EBX=00000000 ECX=0000b7a6 EDX=00000000
ESI=0000b7a6 EDI=00000000 EBP=0000b7a6 ESP=00006f98
EIP=0000b7c2 EFL=00000246 [---Z-P-] CPL=0 II=0 A20=1 SMM=0 HLT=1
ES =d900 000d9000 0000ffff 00009300
CS =f000 000f0000 0000ffff 00009b00
SS =0000 00000000 0000ffff 00009300
DS =0000 00000000 0000ffff 00009300
FS =0000 00000000 0000ffff 00009300
GS =0000 00000000 0000ffff 00009300
LDT=0000 00000000 0000ffff 00008200
TR =0000 00000000 0000ffff 00008b00
GDT=     00000000 00000000
IDT=     00000000 000003ff
CR0=00000010 CR2=00000000 CR3=00000000 CR4=00000000
DR0=0000000000000000 DR1=0000000000000000 DR2=0000000000000000 DR3=0000000000000000 
DR6=00000000ffff0ff0 DR7=0000000000000400
EFER=0000000000000000
FCW=037f FSW=0000 [ST=0] FTW=00 MXCSR=00001f80
FPR0=0000000000000000 0000 FPR1=0000000000000000 0000
FPR2=0000000000000000 0000 FPR3=0000000000000000 0000
FPR4=0000000000000000 0000 FPR5=0000000000000000 0000
FPR6=0000000000000000 0000 FPR7=0000000000000000 0000
XMM00=0000000000000000 0000000000000000 XMM01=0000000000000000 0000000000000000
XMM02=0000000000000000 0000000000000000 XMM03=0000000000000000 0000000000000000
XMM04=0000000000000000 0000000000000000 XMM05=0000000000000000 0000000000000000
XMM06=0000000000000000 0000000000000000 XMM07=0000000000000000 0000000000000000
(qemu) i[K[Din[K[D[Dinf[K[D[D[Dinfo[K[D[D[D[Dinfo [K[D[D[D[D[Dinfo m[K[D[D[D[D[D[Dinfo me[K[D[D[D[D[D[D[Dinfo mem[K[D[D[D[D[D[D[D[Dinfo memo[K[D[D[D[D[D[D[D[D[Dinfo memor[K[D[D[D[D[D[D[D[D[D[Dinfo memory[K
unknown command: 'info memory'
(qemu) i[K[Din[K[D[Dinf[K[D[D[Dinfo[K[D[D[D[Dinfo [K[D[D[D[D[Dinfo m[K[D[D[D[D[D[Dinfo me[K[D[D[D[D[D[D[Dinfo mem[K[D[D[D[D[D[D[D[Dinfo memd[K[D[D[D[D[D[D[D[D[Dinfo memde[K[D[D[D[D[D[D[D[D[D[Dinfo memdev[K
memory backend: ram
  size:  262144000
  merge: false
  dump: true
  prealloc: false
  share: false
  policy: default
  host nodes: 

(qemu) i[K[Din[K[D[Dinf[K[D[D[Dinfo[K[D[D[D[Dinfo [K[D[D[D[D[Dinfo c[K[D[D[D[D[D[Dinfo cp[K[D[D[D[D[D[D[Dinfo cpu[K[D[D[D[D[D[D[D[Dinfo cpus[K
* CPU #0: thread_id=43889
(qemu) i[K[Din[K[D[Dinf[K[D[D[Dinfo[K[D[D[D[Dinfo [K[D[D[D[D[Dinfo n[K[D[D[D[D[D[Dinfo ne[K[D[D[D[D[D[D[Dinfo net[K[D[D[D[D[D[D[D[Dinfo netw[K[D[D[D[D[D[D[D[D[Dinfo netwo[K[D[D[D[D[D[D[D[D[D[Dinfo networ[K[D[D[D[D[D[D[D[D[D[D[Dinfo network[K
virtio-net-pci.0: index=0,type=nic,model=virtio-net-pci,macaddr=52:54:00:12:34:56
 \ net0: index=0,type=user,net=********,restrict=on
(qemu) i[K[Din[K[D[Dinf[K[D[D[Dinfo[K[D[D[D[Dinfo [K[D[D[D[D[Dinfo q[K[D[D[D[D[D[Dinfo qt[K[D[D[D[D[D[D[Dinfo qtr[K[D[D[D[D[D[D[D[Dinfo qtre[K[D[D[D[D[D[D[D[D[Dinfo qtree[K
bus: main-system-bus
  type System
  dev: ps2-mouse, id ""
    gpio-out "" 1
  dev: ps2-kbd, id ""
    gpio-out "" 1
  dev: hpet, id ""
    gpio-in "" 2
    gpio-out "" 1
    gpio-out "sysbus-irq" 32
    timers = 3 (0x3)
    msi = false
    hpet-intcap = 16711940 (0xff0104)
    hpet-offset-saved = true
    mmio 00000000fed00000/0000000000000400
  dev: ioapic, id ""
    gpio-in "" 24
    version = 32 (0x20)
    mmio 00000000fec00000/0000000000001000
  dev: q35-pcihost, id ""
    MCFG = 2952790016 (0xb0000000)
    pci-hole64-size = 34359738368 (32 GiB)
    below-4g-mem-size = 262144000 (250 MiB)
    above-4g-mem-size = 0 (0 B)
    smm-ranges = true
    x-pci-hole64-fix = true
    x-config-reg-migration-enabled = true
    bypass-iommu = false
    bus: pcie.0
      type PCIE
      dev: virtio-net-pci, id ""
        disable-legacy = "off"
        disable-modern = false
        ioeventfd = true
        vectors = 4 (0x4)
        virtio-pci-bus-master-bug-migration = false
        migrate-extra = true
        modern-pio-notify = false
        x-disable-pcie = false
        page-per-vq = false
        x-ignore-backend-features = false
        ats = false
        x-ats-page-aligned = true
        x-pcie-deverr-init = true
        x-pcie-lnkctl-init = true
        x-pcie-pm-init = true
        x-pcie-pm-no-soft-reset = false
        x-pcie-flr-init = true
        aer = false
        addr = 02.0
        romfile = "efi-virtio.rom"
        romsize = 262144 (0x40000)
        rombar = -1 (0xffffffffffffffff)
        multifunction = false
        x-pcie-lnksta-dllla = true
        x-pcie-extcap-init = true
        failover_pair_id = ""
        acpi-index = 0 (0x0)
        x-pcie-err-unc-mask = true
        x-pcie-ari-nextfn-1 = false
        x-max-bounce-buffer-size = 4096 (4 KiB)
        x-pcie-ext-tag = true
        busnr = 0 (0x0)
        class Ethernet controller, addr 00:02.0, pci id 1af4:1000 (sub 1af4:0001)
        bar 0: i/o at 0xc040 [0xc05f]
        bar 1: mem at 0xfebd1000 [0xfebd1fff]
        bar 4: mem at 0xfe000000 [0xfe003fff]
        bar 6: mem at 0xffffffffffffffff [0x3fffe]
        bus: virtio-bus
          type virtio-pci-bus
          dev: virtio-net-device, id ""
            csum = true
            guest_csum = true
            gso = true
            guest_tso4 = true
            guest_tso6 = true
            guest_ecn = true
            guest_ufo = true
            guest_announce = true
            host_tso4 = true
            host_tso6 = true
            host_ecn = true
            host_ufo = true
            mrg_rxbuf = true
            status = true
            ctrl_vq = true
            ctrl_rx = true
            ctrl_vlan = true
            ctrl_rx_extra = true
            ctrl_mac_addr = true
            ctrl_guest_offloads = true
            mq = false
            rss = false
            hash = false
            ebpf-rss-fds = <null>
            guest_rsc_ext = false
            rsc_interval = 300000 (0x493e0)
            mac = "52:54:00:12:34:56"
            netdev = "net0"
            x-txtimer = 150000 (0x249f0)
            x-txburst = 256 (0x100)
            tx = ""
            rx_queue_size = 256 (0x100)
            tx_queue_size = 256 (0x100)
            host_mtu = 0 (0x0)
            x-mtu-bypass-backend = true
            speed = -1 (0xffffffffffffffff)
            duplex = ""
            failover = false
            guest_uso4 = true
            guest_uso6 = true
            host_uso = true
            indirect_desc = true
            event_idx = true
            notify_on_empty = true
            any_layout = true
            iommu_platform = false
            packed = false
            queue_reset = true
            in_order = false
            use-started = true
            use-disabled-flag = true
            x-disable-legacy-check = false
      dev: VGA, id ""
        vgamem_mb = 16 (0x10)
        mmio = true
        qemu-extended-regs = true
        edid = true
        xres = 1280 (0x500)
        yres = 800 (0x320)
        xmax = 0 (0x0)
        ymax = 0 (0x0)
        refresh_rate = 0 (0x0)
        global-vmstate = false
        addr = 01.0
        romfile = "vgabios-stdvga.bin"
        romsize = 65536 (0x10000)
        rombar = -1 (0xffffffffffffffff)
        multifunction = false
        x-pcie-lnksta-dllla = true
        x-pcie-extcap-init = true
        failover_pair_id = ""
        acpi-index = 0 (0x0)
        x-pcie-err-unc-mask = true
        x-pcie-ari-nextfn-1 = false
        x-max-bounce-buffer-size = 4096 (4 KiB)
        x-pcie-ext-tag = true
        busnr = 0 (0x0)
        class VGA controller, addr 00:01.0, pci id 1234:1111 (sub 1af4:1100)
        bar 0: mem at 0xfd000000 [0xfdffffff]
        bar 2: mem at 0xfebd0000 [0xfebd0fff]
        bar 6: mem at 0xffffffffffffffff [0xfffe]
      dev: ICH9-SMB, id ""
        addr = 1f.3
        romfile = ""
        romsize = ********** (0xffffffff)
        rombar = -1 (0xffffffffffffffff)
        multifunction = true
        x-pcie-lnksta-dllla = true
        x-pcie-extcap-init = true
        failover_pair_id = ""
        acpi-index = 0 (0x0)
        x-pcie-err-unc-mask = true
        x-pcie-ari-nextfn-1 = false
        x-max-bounce-buffer-size = 4096 (4 KiB)
        x-pcie-ext-tag = true
        busnr = 0 (0x0)
        class SMBus, addr 00:1f.3, pci id 8086:2930 (sub 1af4:1100)
        bar 4: i/o at 0x700 [0x73f]
        bus: i2c
          type i2c-bus
          dev: smbus-eeprom, id ""
            address = 87 (0x57)
          dev: smbus-eeprom, id ""
            address = 86 (0x56)
          dev: smbus-eeprom, id ""
            address = 85 (0x55)
          dev: smbus-eeprom, id ""
            address = 84 (0x54)
          dev: smbus-eeprom, id ""
            address = 83 (0x53)
          dev: smbus-eeprom, id ""
            address = 82 (0x52)
          dev: smbus-eeprom, id ""
            address = 81 (0x51)
          dev: smbus-eeprom, id ""
            address = 80 (0x50)
      dev: ich9-ahci, id ""
        addr = 1f.2
        romfile = ""
        romsize = ********** (0xffffffff)
        rombar = -1 (0xffffffffffffffff)
        multifunction = true
        x-pcie-lnksta-dllla = true
        x-pcie-extcap-init = true
        failover_pair_id = ""
        acpi-index = 0 (0x0)
        x-pcie-err-unc-mask = true
        x-pcie-ari-nextfn-1 = false
        x-max-bounce-buffer-size = 4096 (4 KiB)
        x-pcie-ext-tag = true
        busnr = 0 (0x0)
        class SATA controller, addr 00:1f.2, pci id 8086:2922 (sub 1af4:1100)
        bar 4: i/o at 0xc060 [0xc07f]
        bar 5: mem at 0xfebd2000 [0xfebd2fff]
        bus: ide.5
          type IDE
        bus: ide.4
          type IDE
        bus: ide.3
          type IDE
        bus: ide.2
          type IDE
          dev: ide-cd, id ""
            drive = "ide2-cd0"
            backend_defaults = "auto"
            logical_block_size = 512 (512 B)
            physical_block_size = 512 (512 B)
            min_io_size = 0 (0 B)
            opt_io_size = 0 (0 B)
            discard_granularity = 512 (512 B)
            write-cache = "auto"
            share-rw = false
            account-invalid = "auto"
            account-failed = "auto"
            rerror = "auto"
            werror = "auto"
            ver = "2.5+"
            wwn = 0 (0x0)
            serial = "QM00005"
            model = ""
            unit = 0 (0x0)
            win2k-install-hack = false
        bus: ide.1
          type IDE
        bus: ide.0
          type IDE
      dev: ICH9-LPC, id ""
        gpio-out "gsi" 24
        noreboot = false
        smm-compat = false
        smm-enabled = true
        x-smi-broadcast = true
        x-smi-cpu-hotplug = true
        x-smi-cpu-hotunplug = true
        x-smi-swsmi-timer = true
        x-smi-periodic-timer = true
        addr = 1f.0
        romfile = ""
        romsize = ********** (0xffffffff)
        rombar = -1 (0xffffffffffffffff)
        multifunction = true
        x-pcie-lnksta-dllla = true
        x-pcie-extcap-init = true
        failover_pair_id = ""
        acpi-index = 0 (0x0)
        x-pcie-err-unc-mask = true
        x-pcie-ari-nextfn-1 = false
        x-max-bounce-buffer-size = 4096 (4 KiB)
        x-pcie-ext-tag = true
        busnr = 0 (0x0)
        class ISA bridge, addr 00:1f.0, pci id 8086:2918 (sub 1af4:1100)
        bus: isa.0
          type ISA
          dev: port92, id ""
            gpio-out "a20" 1
          dev: vmmouse, id ""
          dev: vmport, id ""
            x-read-set-eax = true
            x-signal-unsupported-cmd = true
            x-report-vmx-type = true
            x-cmds-v2 = true
            vmware-vmx-version = 6 (0x6)
            vmware-vmx-type = 2 (0x2)
          dev: i8042, id ""
            gpio-in "ps2-mouse-input-irq" 1
            gpio-in "ps2-kbd-input-irq" 1
            gpio-out "" 2
            gpio-out "a20" 1
            extended-state = true
            kbd-throttle = false
            kbd-irq = 1 (0x1)
            mouse-irq = 12 (0xc)
          dev: isa-parallel, id ""
            index = 0 (0x0)
            iobase = 888 (0x378)
            irq = 7 (0x7)
            chardev = "parallel0"
          dev: isa-pcspk, id ""
            audiodev = ""
            iobase = 97 (0x61)
            migrate = true
          dev: isa-pit, id ""
            gpio-in "" 1
            gpio-out "" 1
            iobase = 64 (0x40)
          dev: isa-i8259, id ""
            gpio-in "" 8
            gpio-out "" 1
            iobase = 160 (0xa0)
            elcr_addr = 1233 (0x4d1)
            elcr_mask = 222 (0xde)
            master = false
          dev: isa-i8259, id ""
            gpio-in "" 8
            gpio-out "" 1
            iobase = 32 (0x20)
            elcr_addr = 1232 (0x4d0)
            elcr_mask = 248 (0xf8)
            master = true
          dev: mc146818rtc, id ""
            gpio-out "" 1
            base_year = 0 (0x0)
            iobase = 112 (0x70)
            irq = 8 (0x8)
            lost_tick_policy = "discard"
          dev: i8257, id ""
            base = 192 (0xc0)
            page-base = 136 (0x88)
            pageh-base = -1 (0xffffffffffffffff)
            dshift = 1 (0x1)
          dev: i8257, id ""
            base = 0 (0x0)
            page-base = 128 (0x80)
            pageh-base = -1 (0xffffffffffffffff)
            dshift = 0 (0x0)
      dev: mch, id ""
        extended-tseg-mbytes = 16 (0x10)
        smbase-smram = true
        addr = 00.0
        romfile = ""
        romsize = ********** (0xffffffff)
        rombar = -1 (0xffffffffffffffff)
        multifunction = false
        x-pcie-lnksta-dllla = true
        x-pcie-extcap-init = true
        failover_pair_id = ""
        acpi-index = 0 (0x0)
        x-pcie-err-unc-mask = true
        x-pcie-ari-nextfn-1 = false
        x-max-bounce-buffer-size = 4096 (4 KiB)
        x-pcie-ext-tag = true
        busnr = 0 (0x0)
        class Host bridge, addr 00:00.0, pci id 8086:29c0 (sub 1af4:1100)
  dev: fw_cfg_io, id ""
    dma_enabled = true
    x-file-slots = 32 (0x20)
    acpi-mr-restore = true
  dev: kvmvapic, id ""
(qemu) 