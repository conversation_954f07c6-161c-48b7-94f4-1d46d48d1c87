# Logs Directory

This directory contains log files from various testing and development activities.

## Directory Structure

```
logs/
├── README.md                           # This file
└── qemu-sessions/                      # QEMU testing session logs
```

## QEMU Session Logs

The `qemu-sessions/` directory contains logs from QEMU testing sessions:

- **qemu-interactive-*.log**: Interactive QEMU session logs
- **qemu-minimal-*.log**: Minimal kernel boot logs
- **qemu-simple-test-*.log**: Simple test execution logs
- **qemu-monitor-session-*.log**: QEMU monitor session logs

## Log Format

Logs are named with timestamps for easy identification:
- Format: `qemu-{type}-{YYYYMMDD-HHMMSS}.log`
- Example: `qemu-interactive-20250628-150856.log`

## Usage

These logs are useful for:

1. **Debugging**: Analyzing boot sequences and error messages
2. **Performance Analysis**: Tracking boot times and resource usage
3. **Regression Testing**: Comparing behavior across different builds
4. **Documentation**: Recording successful test scenarios

## Log Retention

- Logs are kept for historical reference
- Old logs can be archived or removed as needed
- Critical logs should be preserved for troubleshooting

## Generating New Logs

QEMU scripts automatically generate logs when run:

```bash
# Interactive session (creates qemu-interactive-*.log)
./scripts/qemu-interactive.sh

# Simple test (creates qemu-simple-test-*.log)
./scripts/qemu-simple-test.sh

# Monitor session (creates qemu-monitor-session-*.log)
./scripts/qemu-monitor-interactive.sh
```

## Notes

- All logs are in plain text format
- Logs may contain ANSI escape sequences for colors
- Large logs should be compressed for storage efficiency
- Sensitive information should be redacted from logs before sharing
