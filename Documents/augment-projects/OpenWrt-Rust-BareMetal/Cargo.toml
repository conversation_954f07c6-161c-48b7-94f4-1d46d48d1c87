[package]
name = "openwrt-rust-firmware"
version = "0.1.0"
edition = "2021"
authors = ["OpenWrt Rust Migration Team"]
description = "Optimized OpenWrt core components migrated to Rust bare-metal implementation"
license = "GPL-2.0"

[lib]
name = "openwrt_rust_firmware"
path = "src/lib.rs"

[[bin]]
name = "kernel"
path = "src/main.rs"

[[bin]]
name = "openwrt-userspace"
path = "src/userspace_main.rs"

[[bin]]
name = "qemu-kernel"
path = "src/qemu_main.rs"

[[bin]]
name = "simple-qemu-test"
path = "src/simple_qemu_test.rs"

[[bin]]
name = "interactive-kernel"
path = "src/interactive_kernel.rs"

[dependencies]
# Core embedded dependencies
spin = { version = "0.9", default-features = false, features = ["mutex", "spin_mutex"] }
linked_list_allocator = { version = "0.10", default-features = false, features = ["use_spin"] }
volatile = { version = "0.4", default-features = false }
bitflags = { version = "2.4", default-features = false }
nb = { version = "1.1", default-features = false }

# Embedded HAL and utilities
embedded-hal = { version = "1.0", default-features = false }
embedded-hal-async = { version = "1.0", default-features = false, optional = true }
embedded-storage = { version = "0.3", default-features = false }
embedded-io = { version = "0.6", default-features = false }
heapless = { version = "0.8", default-features = false, features = ["serde"] }
cortex-m = { version = "0.7", optional = true }

# Architecture-specific support
x86_64 = { version = "0.15", default-features = false, features = ["instructions"], optional = true }
lazy_static = { version = "1.4", features = ["spin_no_std"], default-features = false }

# Networking (no_std compatible)
smoltcp = { version = "0.11", default-features = false, features = [
    "proto-ipv4", "proto-ipv6", "proto-igmp",
    "socket-tcp", "socket-udp", "socket-icmp", "socket-dhcpv4", "socket-dns",
    "medium-ethernet", "medium-ip"
] }

# Serialization for configuration
serde = { version = "1.0", default-features = false, features = ["derive"] }
postcard = { version = "1.0", default-features = false, features = ["heapless"] }

# Parser combinator for UCI configuration parsing
nom = { version = "7.1", default-features = false, features = ["alloc"] }

# FFI support for C integration
bindgen = { version = "0.69", optional = true }

[build-dependencies]
cc = "1.0"
bindgen = "0.69"

[dev-dependencies]
# Testing dependencies for Phase 5 Production Readiness
criterion = { version = "0.5", default-features = false }
proptest = { version = "1.0", default-features = false }
serial_test = "3.0"

[features]
default = ["x86_64"]
# Architecture-specific features
cortex-m = ["dep:cortex-m"]
x86_64 = ["dep:x86_64"]
# FFI features for C integration
ffi = ["bindgen"]
# Async features
async = ["embedded-hal-async"]
# Boot environment features
qemu-boot = ["x86_64"]
podman-userspace = ["ffi", "std"]
bare-metal = []
std = []
# Security features
security-audit = []
# Profiling and debugging
profiling = []
# Performance optimization features
performance-monitoring = []
benchmarking = []
security-hardening = []
system-integration = []
# Phase 5 Production Readiness features
comprehensive-testing = []
stress-testing = []
hardware-testing = []
regression-testing = []
documentation-generation = []

[profile.dev]
panic = "abort"
opt-level = 0
debug = true
lto = false

[profile.release]
panic = "abort"
opt-level = "z"  # Optimize aggressively for size
debug = false
lto = "fat"      # Full LTO for maximum size reduction
codegen-units = 1
strip = "symbols"
overflow-checks = false  # Disable overflow checks for size
incremental = false

# Target-specific configurations moved to .cargo/config.toml

# Size optimization profile for embedded deployment
[profile.embedded]
inherits = "release"
opt-level = "z"
lto = "fat"
codegen-units = 1
panic = "abort"
strip = "symbols"
overflow-checks = false
debug-assertions = false

# Performance profile for benchmarking
[profile.bench-optimized]
inherits = "release"
opt-level = 3
lto = "thin"
codegen-units = 16
debug = false
