//! Memory management for OpenWrt Rust kernel
//! 
//! Provides virtual memory management, page allocation, and memory protection
//! suitable for embedded OpenWrt devices.

// use core::ptr::NonNull; // Unused for now
use spin::Mutex;
use bitflags::bitflags;

/// Page size for memory management (4KB standard)
pub const PAGE_SIZE: usize = 4096;

/// Maximum number of pages we can manage
const MAX_PAGES: usize = 1024; // 4MB total addressable memory

/// Physical memory frame allocator
pub struct FrameAllocator {
    frames: [bool; MAX_PAGES], // true = allocated, false = free
    next_free: usize,
}

impl FrameAllocator {
    /// Create a new frame allocator
    pub const fn new() -> Self {
        Self {
            frames: [false; MAX_PAGES],
            next_free: 0,
        }
    }
    
    /// Allocate a physical frame
    pub fn allocate(&mut self) -> Option<PhysicalAddress> {
        for i in self.next_free..MAX_PAGES {
            if !self.frames[i] {
                self.frames[i] = true;
                self.next_free = i + 1;
                return Some(PhysicalAddress::new(i * PAGE_SIZE));
            }
        }
        
        // Wrap around and search from beginning
        for i in 0..self.next_free {
            if !self.frames[i] {
                self.frames[i] = true;
                self.next_free = i + 1;
                return Some(PhysicalAddress::new(i * PAGE_SIZE));
            }
        }
        
        None // Out of memory
    }
    
    /// Free a physical frame
    pub fn deallocate(&mut self, addr: PhysicalAddress) {
        let frame_index = addr.as_usize() / PAGE_SIZE;
        if frame_index < MAX_PAGES {
            self.frames[frame_index] = false;
            if frame_index < self.next_free {
                self.next_free = frame_index;
            }
        }
    }
    
    /// Get memory usage statistics
    pub fn usage_stats(&self) -> MemoryUsage {
        let allocated = self.frames.iter().filter(|&&used| used).count();
        MemoryUsage {
            total_frames: MAX_PAGES,
            allocated_frames: allocated,
            free_frames: MAX_PAGES - allocated,
            total_bytes: MAX_PAGES * PAGE_SIZE,
            allocated_bytes: allocated * PAGE_SIZE,
            free_bytes: (MAX_PAGES - allocated) * PAGE_SIZE,
        }
    }
}

/// Global frame allocator
static FRAME_ALLOCATOR: Mutex<FrameAllocator> = Mutex::new(FrameAllocator::new());

/// Physical address wrapper
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub struct PhysicalAddress(usize);

impl PhysicalAddress {
    /// Create a new physical address
    pub const fn new(addr: usize) -> Self {
        Self(addr)
    }
    
    /// Get the address as usize
    pub const fn as_usize(self) -> usize {
        self.0
    }
    
    /// Get the address as a pointer
    pub fn as_ptr<T>(self) -> *const T {
        self.0 as *const T
    }
    
    /// Get the address as a mutable pointer
    pub fn as_mut_ptr<T>(self) -> *mut T {
        self.0 as *mut T
    }
    
    /// Check if address is page-aligned
    pub fn is_page_aligned(self) -> bool {
        self.0 % PAGE_SIZE == 0
    }
    
    /// Align address down to page boundary
    pub fn page_align_down(self) -> Self {
        Self(self.0 & !(PAGE_SIZE - 1))
    }
    
    /// Align address up to page boundary
    pub fn page_align_up(self) -> Self {
        Self((self.0 + PAGE_SIZE - 1) & !(PAGE_SIZE - 1))
    }
}

/// Virtual address wrapper
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub struct VirtualAddress(usize);

impl VirtualAddress {
    /// Create a new virtual address
    pub const fn new(addr: usize) -> Self {
        Self(addr)
    }
    
    /// Get the address as usize
    pub const fn as_usize(self) -> usize {
        self.0
    }
    
    /// Get the address as a pointer
    pub fn as_ptr<T>(self) -> *const T {
        self.0 as *const T
    }
    
    /// Get the address as a mutable pointer
    pub fn as_mut_ptr<T>(self) -> *mut T {
        self.0 as *mut T
    }
}

bitflags! {
    /// Page table entry flags
    pub struct PageFlags: u64 {
        const PRESENT = 1 << 0;
        const WRITABLE = 1 << 1;
        const USER_ACCESSIBLE = 1 << 2;
        const WRITE_THROUGH = 1 << 3;
        const NO_CACHE = 1 << 4;
        const ACCESSED = 1 << 5;
        const DIRTY = 1 << 6;
        const HUGE_PAGE = 1 << 7;
        const GLOBAL = 1 << 8;
        const NO_EXECUTE = 1 << 63;
    }
}

/// Memory usage statistics
#[derive(Debug, Clone, Copy)]
pub struct MemoryUsage {
    pub total_frames: usize,
    pub allocated_frames: usize,
    pub free_frames: usize,
    pub total_bytes: usize,
    pub allocated_bytes: usize,
    pub free_bytes: usize,
}

/// Initialize memory management
pub fn init() {
    
    
    
}

/// Allocate a physical page with safety checks
pub fn allocate_frame() -> Option<PhysicalAddress> {
    let mut allocator = FRAME_ALLOCATOR.lock();

    // Check if we're running low on memory
    if is_low_memory_internal(&allocator) {
        // Log warning or trigger cleanup
        return None;
    }

    allocator.allocate()
}

/// Internal low memory check without additional locking
fn is_low_memory_internal(allocator: &FrameAllocator) -> bool {
    let usage = allocator.usage_stats();
    let usage_percent = (usage.allocated_frames * 100) / usage.total_frames;
    usage_percent > 85 // More conservative threshold
}

/// Free a physical page
pub fn deallocate_frame(addr: PhysicalAddress) {
    FRAME_ALLOCATOR.lock().deallocate(addr);
}

/// Get current memory usage
pub fn get_memory_usage() -> MemoryUsage {
    FRAME_ALLOCATOR.lock().usage_stats()
}

/// Get current memory usage as bytes (for compatibility)
pub fn get_usage() -> u64 {
    let usage = get_memory_usage();
    usage.allocated_bytes as u64
}

/// Check if system is low on memory
pub fn is_low_memory() -> bool {
    let usage = get_memory_usage();
    let usage_percent = (usage.allocated_frames * 100) / usage.total_frames;
    usage_percent > 80 // Consider low memory if > 80% used
}

/// Memory region descriptor
#[derive(Debug, Clone, Copy)]
pub struct MemoryRegion {
    pub start: PhysicalAddress,
    pub size: usize,
    pub region_type: MemoryRegionType,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum MemoryRegionType {
    Available,
    Reserved,
    AcpiReclaimable,
    AcpiNvs,
    BadMemory,
    Kernel,
    Module,
}

/// Simple memory mapper for embedded systems
pub struct MemoryMapper {
    // In a full implementation, this would contain page tables
    // For now, we use identity mapping
}

impl MemoryMapper {
    /// Create a new memory mapper
    pub const fn new() -> Self {
        Self {}
    }
    
    /// Map a virtual address to a physical address
    pub fn map(&mut self, virt: VirtualAddress, phys: PhysicalAddress, flags: PageFlags) -> Result<(), MapError> {
        // For embedded systems, we often use identity mapping
        // In a full implementation, this would set up page tables
        Ok(())
    }
    
    /// Unmap a virtual address
    pub fn unmap(&mut self, virt: VirtualAddress) -> Result<(), MapError> {
        Ok(())
    }
    
    /// Translate virtual address to physical address
    pub fn translate(&self, virt: VirtualAddress) -> Option<PhysicalAddress> {
        // Identity mapping for simplicity
        Some(PhysicalAddress::new(virt.as_usize()))
    }
}

/// Memory mapping errors
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum MapError {
    PageAlreadyMapped,
    PageNotMapped,
    OutOfMemory,
    InvalidAddress,
}

/// Global memory mapper
static MEMORY_MAPPER: Mutex<MemoryMapper> = Mutex::new(MemoryMapper::new());
