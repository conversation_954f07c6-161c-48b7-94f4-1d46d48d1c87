//! Foreign Function Interface (FFI) integration for OpenWrt C libraries
//!
//! This module provides safe Rust bindings for core OpenWrt C libraries,
//! enabling integration with the existing OpenWrt ecosystem while maintaining
//! memory safety and Rust's ownership model.

use core::ffi::{c_char, c_int, c_void};
use core::ptr;
use heapless::{String as HeaplessString, Vec as HeaplessVec};

pub mod simple_test;

// FFI modules now enabled with proper string handling
pub mod libubox;
pub mod uci;
pub mod netifd;

/// Common FFI error types
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum FfiError {
    /// Null pointer passed where valid pointer expected
    NullPointer,
    /// Invalid parameter value
    InvalidParameter,
    /// Memory allocation failed
    OutOfMemory,
    /// Operation not supported
    NotSupported,
    /// Permission denied
    PermissionDenied,
    /// Resource busy or locked
    Busy,
    /// Operation timed out
    Timeout,
    /// Unknown error from C library
    Unknown(c_int),
}

impl FfiError {
    /// Convert C error code to FfiError
    pub fn from_c_error(code: c_int) -> Self {
        match code {
            0 => panic!("Success code should not be converted to error"),
            -1 => FfiError::Unknown(-1),
            -2 => FfiError::InvalidParameter,
            -3 => FfiError::OutOfMemory,
            -4 => FfiError::NotSupported,
            -5 => FfiError::PermissionDenied,
            -6 => FfiError::Busy,
            -7 => FfiError::Timeout,
            other => FfiError::Unknown(other),
        }
    }
}

/// Result type for FFI operations
pub type FfiResult<T> = Result<T, FfiError>;

/// Safe wrapper for C string handling in no_std environment
pub struct CString {
    ptr: *mut c_char,
    owned: bool,
    len: usize,
}

impl CString {
    /// Create a new CString from a Rust string
    pub fn new(s: &str) -> FfiResult<Self> {
        if s.is_empty() {
            return Ok(CString {
                ptr: ptr::null_mut(),
                owned: false,
                len: 0,
            });
        }

        // Allocate memory for the string + null terminator
        let len = s.len();
        let total_size = len + 1;

        // Use our custom allocator to allocate memory
        let layout = core::alloc::Layout::from_size_align(total_size, 1)
            .map_err(|_| FfiError::InvalidParameter)?;

        let ptr = unsafe {
            crate::allocator::ALLOCATOR.alloc(layout) as *mut c_char
        };

        if ptr.is_null() {
            return Err(FfiError::OutOfMemory);
        }

        // Copy the string data and add null terminator
        unsafe {
            ptr::copy_nonoverlapping(s.as_ptr(), ptr as *mut u8, len);
            *((ptr as *mut u8).add(len)) = 0; // null terminator
        }

        Ok(CString {
            ptr,
            owned: true,
            len,
        })
    }

    /// Create a CString from a raw C pointer (not owned)
    pub unsafe fn from_raw(ptr: *mut c_char) -> Self {
        let len = if ptr.is_null() {
            0
        } else {
            // Calculate length by finding null terminator
            let mut len = 0;
            let mut current = ptr;
            while *current != 0 {
                len += 1;
                current = current.add(1);
            }
            len
        };

        CString {
            ptr,
            owned: false,
            len,
        }
    }

    /// Get the raw pointer
    pub fn as_ptr(&self) -> *const c_char {
        self.ptr as *const c_char
    }

    /// Get the length of the string (without null terminator)
    pub fn len(&self) -> usize {
        self.len
    }

    /// Check if the string is empty
    pub fn is_empty(&self) -> bool {
        self.len == 0
    }

    /// Convert to Rust string slice (if valid UTF-8)
    pub fn to_str(&self) -> Option<&str> {
        if self.ptr.is_null() || self.len == 0 {
            return Some("");
        }

        unsafe {
            let slice = core::slice::from_raw_parts(self.ptr as *const u8, self.len);
            core::str::from_utf8(slice).ok()
        }
    }

    /// Convert to HeaplessString for no_std compatibility
    pub fn to_heapless_string<const N: usize>(&self) -> FfiResult<HeaplessString<N>> {
        if let Some(s) = self.to_str() {
            HeaplessString::try_from(s).map_err(|_| FfiError::InvalidParameter)
        } else {
            Err(FfiError::InvalidParameter)
        }
    }

    /// Create from HeaplessString
    pub fn from_heapless_string<const N: usize>(s: &HeaplessString<N>) -> FfiResult<Self> {
        Self::new(s.as_str())
    }
}

impl Drop for CString {
    fn drop(&mut self) {
        if self.owned && !self.ptr.is_null() {
            // Free the allocated memory using our custom allocator
            let total_size = self.len + 1;
            let layout = core::alloc::Layout::from_size_align(total_size, 1)
                .expect("Invalid layout in CString::drop");

            unsafe {
                crate::allocator::ALLOCATOR.dealloc(self.ptr as *mut u8, layout);
            }
        }
    }
}

/// Safe wrapper for C void pointers with size tracking
pub struct CVoidPtr {
    ptr: *mut c_void,
    owned: bool,
    size: usize,
}

impl CVoidPtr {
    /// Create from raw pointer (not owned)
    pub unsafe fn from_raw(ptr: *mut c_void) -> Self {
        CVoidPtr {
            ptr,
            owned: false,
            size: 0, // Unknown size for external pointers
        }
    }

    /// Create from raw pointer with known size (not owned)
    pub unsafe fn from_raw_with_size(ptr: *mut c_void, size: usize) -> Self {
        CVoidPtr {
            ptr,
            owned: false,
            size,
        }
    }

    /// Allocate new memory block
    pub fn alloc(size: usize) -> FfiResult<Self> {
        if size == 0 {
            return Ok(CVoidPtr {
                ptr: ptr::null_mut(),
                owned: false,
                size: 0,
            });
        }

        let layout = core::alloc::Layout::from_size_align(size, 8)
            .map_err(|_| FfiError::InvalidParameter)?;

        let ptr = unsafe {
            crate::allocator::ALLOCATOR.alloc(layout) as *mut c_void
        };

        if ptr.is_null() {
            return Err(FfiError::OutOfMemory);
        }

        Ok(CVoidPtr {
            ptr,
            owned: true,
            size,
        })
    }

    /// Get the raw pointer
    pub fn as_ptr(&self) -> *mut c_void {
        self.ptr
    }

    /// Get the size of the allocated block (if known)
    pub fn size(&self) -> usize {
        self.size
    }

    /// Check if pointer is null
    pub fn is_null(&self) -> bool {
        self.ptr.is_null()
    }

    /// Cast to specific type pointer
    pub unsafe fn cast<T>(&self) -> *mut T {
        self.ptr as *mut T
    }

    /// Write data to the pointer (if owned and size is sufficient)
    pub unsafe fn write_bytes(&mut self, data: &[u8]) -> FfiResult<()> {
        if !self.owned {
            return Err(FfiError::PermissionDenied);
        }

        if data.len() > self.size {
            return Err(FfiError::InvalidParameter);
        }

        if self.ptr.is_null() {
            return Err(FfiError::NullPointer);
        }

        ptr::copy_nonoverlapping(data.as_ptr(), self.ptr as *mut u8, data.len());
        Ok(())
    }

    /// Read data from the pointer
    pub unsafe fn read_bytes(&self, len: usize) -> FfiResult<HeaplessVec<u8, 256>> {
        if self.ptr.is_null() {
            return Err(FfiError::NullPointer);
        }

        if self.size > 0 && len > self.size {
            return Err(FfiError::InvalidParameter);
        }

        let mut vec = HeaplessVec::new();
        let slice = core::slice::from_raw_parts(self.ptr as *const u8, len);

        for &byte in slice {
            vec.push(byte).map_err(|_| FfiError::OutOfMemory)?;
        }

        Ok(vec)
    }
}

impl Drop for CVoidPtr {
    fn drop(&mut self) {
        if self.owned && !self.ptr.is_null() && self.size > 0 {
            let layout = core::alloc::Layout::from_size_align(self.size, 8)
                .expect("Invalid layout in CVoidPtr::drop");

            unsafe {
                crate::allocator::ALLOCATOR.dealloc(self.ptr as *mut u8, layout);
            }
        }
    }
}

/// Initialize FFI subsystem
pub fn init() -> FfiResult<()> {
    // Test basic FFI functionality first
    simple_test::test_ffi_integration()?;

    // Initialize each FFI module in dependency order
    libubox::init()?;
    uci::init()?;
    netifd::init()?;

    Ok(())
}

/// Cleanup FFI subsystem
pub fn cleanup() {
    // Cleanup in reverse order
    netifd::cleanup();
    uci::cleanup();
    libubox::cleanup();
}

/// Check if FFI is available (i.e., running on actual OpenWrt system)
pub fn is_available() -> bool {
    // In a real implementation, this would check for the presence of OpenWrt libraries
    // For now, return false as we're in a development environment
    false
}

/// Get version information for integrated C libraries
pub fn get_versions() -> FfiResult<LibraryVersions> {
    // For now, return placeholder versions
    Ok(LibraryVersions {
        libubox: HeaplessString::try_from("2023.05.12").map_err(|_| FfiError::InvalidParameter)?,
        uci: HeaplessString::try_from("2023.05.12").map_err(|_| FfiError::InvalidParameter)?,
        netifd: HeaplessString::try_from("2023.05.12").map_err(|_| FfiError::InvalidParameter)?,
    })
}

/// Version information for all integrated libraries
#[derive(Debug, Clone)]
pub struct LibraryVersions {
    pub libubox: HeaplessString<32>,
    pub uci: HeaplessString<32>,
    pub netifd: HeaplessString<32>,
}

/// Common trait for FFI wrappers
pub trait FfiWrapper {
    type Handle;

    /// Initialize the wrapper
    fn init() -> FfiResult<()>;

    /// Cleanup the wrapper
    fn cleanup();

    /// Get version information
    fn get_version() -> FfiResult<HeaplessString<32>>;

    /// Check if the underlying C library is available
    fn is_available() -> bool;

    /// Get library-specific error information
    fn get_last_error() -> FfiResult<HeaplessString<128>>;
}

/// String conversion utilities for FFI operations
pub struct FfiStringUtils;

impl FfiStringUtils {
    /// Convert C string to HeaplessString safely
    pub unsafe fn c_str_to_heapless<const N: usize>(
        ptr: *const c_char
    ) -> FfiResult<HeaplessString<N>> {
        if ptr.is_null() {
            return Ok(HeaplessString::new());
        }

        let cstr = CString::from_raw(ptr as *mut c_char);
        cstr.to_heapless_string()
    }

    /// Convert HeaplessString to CString
    pub fn heapless_to_c_str<const N: usize>(
        s: &HeaplessString<N>
    ) -> FfiResult<CString> {
        CString::new(s.as_str())
    }

    /// Validate UTF-8 string for FFI safety
    pub fn validate_utf8(bytes: &[u8]) -> FfiResult<&str> {
        core::str::from_utf8(bytes).map_err(|_| FfiError::InvalidParameter)
    }

    /// Copy string with length limit for safety
    pub fn safe_string_copy(
        src: &str,
        max_len: usize
    ) -> FfiResult<HeaplessString<256>> {
        if src.len() > max_len {
            return Err(FfiError::InvalidParameter);
        }

        HeaplessString::try_from(src).map_err(|_| FfiError::OutOfMemory)
    }
}

/// Memory management utilities for FFI operations
pub struct FfiMemoryUtils;

impl FfiMemoryUtils {
    /// Allocate aligned memory for C structures
    pub fn alloc_aligned(size: usize, align: usize) -> FfiResult<CVoidPtr> {
        let layout = core::alloc::Layout::from_size_align(size, align)
            .map_err(|_| FfiError::InvalidParameter)?;

        let ptr = unsafe {
            crate::allocator::ALLOCATOR.alloc(layout) as *mut c_void
        };

        if ptr.is_null() {
            return Err(FfiError::OutOfMemory);
        }

        Ok(CVoidPtr {
            ptr,
            owned: true,
            size,
        })
    }

    /// Zero-initialize memory block
    pub unsafe fn zero_memory(ptr: *mut c_void, size: usize) -> FfiResult<()> {
        if ptr.is_null() {
            return Err(FfiError::NullPointer);
        }

        ptr::write_bytes(ptr as *mut u8, 0, size);
        Ok(())
    }

    /// Copy memory safely with bounds checking and alignment validation
    pub unsafe fn safe_copy(
        dst: *mut c_void,
        src: *const c_void,
        size: usize,
        dst_size: usize
    ) -> FfiResult<()> {
        if dst.is_null() || src.is_null() {
            return Err(FfiError::NullPointer);
        }

        if size > dst_size {
            return Err(FfiError::InvalidParameter);
        }

        // Check for integer overflow
        if size > isize::MAX as usize {
            return Err(FfiError::InvalidParameter);
        }

        // Validate alignment for better performance and safety
        let dst_addr = dst as usize;
        let src_addr = src as usize;

        // Ensure pointers don't overlap (unless they're identical)
        if dst_addr != src_addr {
            let dst_end = dst_addr.saturating_add(size);
            let src_end = src_addr.saturating_add(size);

            if (dst_addr < src_end && src_addr < dst_end) {
                return Err(FfiError::InvalidParameter);
            }
        }

        ptr::copy_nonoverlapping(src as *const u8, dst as *mut u8, size);
        Ok(())
    }

    /// Safe string conversion with length validation
    pub unsafe fn safe_str_from_ptr(
        ptr: *const c_char,
        max_len: usize
    ) -> FfiResult<&'static str> {
        if ptr.is_null() {
            return Err(FfiError::NullPointer);
        }

        // Find string length safely
        let mut len = 0;
        let mut current = ptr;

        while len < max_len {
            if *current == 0 {
                break;
            }
            current = current.add(1);
            len += 1;
        }

        if len >= max_len {
            return Err(FfiError::InvalidParameter);
        }

        let slice = core::slice::from_raw_parts(ptr as *const u8, len);
        core::str::from_utf8(slice).map_err(|_| FfiError::InvalidParameter)
    }
}

/// Macro for safe FFI function calls
#[macro_export]
macro_rules! safe_ffi_call {
    ($func:expr) => {{
        let result = unsafe { $func };
        if result < 0 {
            Err(FfiError::from_c_error(result))
        } else {
            Ok(result)
        }
    }};
    
    ($func:expr, $success_value:expr) => {{
        let result = unsafe { $func };
        if result == $success_value {
            Ok(())
        } else {
            Err(FfiError::from_c_error(result))
        }
    }};
}

/// Macro for safe pointer validation
#[macro_export]
macro_rules! validate_ptr {
    ($ptr:expr) => {{
        if $ptr.is_null() {
            return Err(FfiError::NullPointer);
        }
    }};
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_ffi_error_conversion() {
        assert_eq!(FfiError::from_c_error(-2), FfiError::InvalidParameter);
        assert_eq!(FfiError::from_c_error(-3), FfiError::OutOfMemory);
        assert_eq!(FfiError::from_c_error(-99), FfiError::Unknown(-99));
    }

    #[test]
    fn test_cstring_creation() {
        // Initialize allocator for testing
        crate::allocator::init();

        let cstr = CString::new("test").unwrap();
        assert!(cstr.owned);
        assert_eq!(cstr.len(), 4);
        assert_eq!(cstr.to_str().unwrap(), "test");
    }

    #[test]
    fn test_cstring_empty() {
        let cstr = CString::new("").unwrap();
        assert!(!cstr.owned);
        assert_eq!(cstr.len(), 0);
        assert!(cstr.is_empty());
    }

    #[test]
    fn test_cstring_to_heapless() {
        crate::allocator::init();

        let cstr = CString::new("hello").unwrap();
        let heapless: HeaplessString<32> = cstr.to_heapless_string().unwrap();
        assert_eq!(heapless.as_str(), "hello");
    }

    #[test]
    fn test_cvoidptr_null_check() {
        let ptr = unsafe { CVoidPtr::from_raw(core::ptr::null_mut()) };
        assert!(ptr.is_null());
        assert_eq!(ptr.size(), 0);
    }

    #[test]
    fn test_cvoidptr_allocation() {
        crate::allocator::init();

        let ptr = CVoidPtr::alloc(64).unwrap();
        assert!(!ptr.is_null());
        assert_eq!(ptr.size(), 64);
    }

    #[test]
    fn test_ffi_string_utils() {
        crate::allocator::init();

        let heapless = HeaplessString::<32>::try_from("test").unwrap();
        let cstr = FfiStringUtils::heapless_to_c_str(&heapless).unwrap();
        assert_eq!(cstr.to_str().unwrap(), "test");
    }

    #[test]
    fn test_safe_string_copy() {
        let result = FfiStringUtils::safe_string_copy("hello", 10).unwrap();
        assert_eq!(result.as_str(), "hello");

        let result = FfiStringUtils::safe_string_copy("too_long_string", 5);
        assert!(result.is_err());
    }

    #[test]
    fn test_memory_utils_aligned_alloc() {
        crate::allocator::init();

        let ptr = FfiMemoryUtils::alloc_aligned(64, 8).unwrap();
        assert!(!ptr.is_null());
        assert_eq!(ptr.size(), 64);
    }
}
