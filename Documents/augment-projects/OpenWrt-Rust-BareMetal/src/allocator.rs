//! Custom allocator implementation for embedded OpenWrt environment
//!
//! This module provides memory allocation capabilities optimized for
//! resource-constrained embedded devices typical in OpenWrt deployments.
//!
//! Phase 12 Milestone 12.1 Week 2 - Memory Usage Optimization and Leak Detection

use linked_list_allocator::LockedHeap;
use core::alloc::{GlobalAlloc, Layout};
use spin::Mutex;
use heapless::{Vec, FnvIndexMap};

/// Global allocator instance
#[global_allocator]
pub static ALLOCATOR: LockedHeap = LockedHeap::empty();

/// Heap memory region for the allocator
static mut HEAP: [u8; HEAP_SIZE] = [0; HEAP_SIZE];

/// Heap size configuration - optimized for embedded devices
/// This can be adjusted based on target device capabilities
const HEAP_SIZE: usize = 64 * 1024; // 64KB heap for embedded systems

/// Memory pool sizes for different allocation patterns
const SMALL_POOL_SIZE: usize = 64;
const MEDIUM_POOL_SIZE: usize = 256;
const LARGE_POOL_SIZE: usize = 1024;

/// Number of pre-allocated blocks per pool
const POOL_BLOCK_COUNT: usize = 32;

/// Cache line size for alignment optimization
const CACHE_LINE_SIZE: usize = 64;

/// Allocation statistics for monitoring and debugging
#[derive(Debug, Clone, Default)]
pub struct AllocationStats {
    pub total_allocated: usize,
    pub total_freed: usize,
    pub current_usage: usize,
    pub peak_usage: usize,
    pub allocation_count: usize,
    pub free_count: usize,
    pub fragmentation_ratio: f32,
    pub largest_free_block: usize,
    pub allocation_failures: usize,
    pub total_size: usize,
    // Phase 12.1 Week 2 - Memory leak detection
    pub potential_leaks: usize,
    pub leak_detection_enabled: bool,
    pub memory_efficiency_percent: f32,
}

/// Memory allocation tracking entry for leak detection
#[derive(Debug, Clone)]
pub struct AllocationEntry {
    pub ptr: usize,
    pub size: usize,
    pub timestamp: u64,
    pub allocation_id: u32,
    pub call_site: &'static str,
}

/// Memory leak detection framework
#[derive(Debug)]
pub struct LeakDetector {
    /// Active allocations tracking
    active_allocations: FnvIndexMap<usize, AllocationEntry, 256>,
    /// Next allocation ID
    next_id: u32,
    /// Leak detection enabled
    enabled: bool,
    /// Leak threshold in seconds
    leak_threshold_seconds: u64,
    /// Maximum tracked allocations
    max_tracked: usize,
}

impl LeakDetector {
    /// Create new leak detector
    pub const fn new() -> Self {
        Self {
            active_allocations: FnvIndexMap::new(),
            next_id: 1,
            enabled: true,
            leak_threshold_seconds: 300, // 5 minutes
            max_tracked: 256,
        }
    }

    /// Track new allocation
    pub fn track_allocation(&mut self, ptr: *mut u8, size: usize, call_site: &'static str) {
        if !self.enabled || ptr.is_null() {
            return;
        }

        let entry = AllocationEntry {
            ptr: ptr as usize,
            size,
            timestamp: crate::profiling::get_timestamp(),
            allocation_id: self.next_id,
            call_site,
        };

        self.next_id = self.next_id.wrapping_add(1);

        // If we're at capacity, remove oldest allocation
        if self.active_allocations.len() >= self.max_tracked {
            if let Some((oldest_ptr, _)) = self.active_allocations.iter().min_by_key(|(_, entry)| entry.timestamp) {
                let oldest_ptr = *oldest_ptr;
                self.active_allocations.remove(&oldest_ptr);
            }
        }

        let _ = self.active_allocations.insert(entry.ptr, entry);
    }

    /// Track deallocation
    pub fn track_deallocation(&mut self, ptr: *mut u8) {
        if !self.enabled || ptr.is_null() {
            return;
        }

        self.active_allocations.remove(&(ptr as usize));
    }

    /// Detect potential memory leaks
    pub fn detect_leaks(&self) -> usize {
        if !self.enabled {
            return 0;
        }

        let current_time = crate::profiling::get_timestamp();
        let threshold_us = self.leak_threshold_seconds * 1_000_000;

        self.active_allocations
            .values()
            .filter(|entry| current_time.saturating_sub(entry.timestamp) > threshold_us)
            .count()
    }

    /// Get leak detection statistics
    pub fn get_stats(&self) -> (usize, usize, usize) {
        let active_count = self.active_allocations.len();
        let potential_leaks = self.detect_leaks();
        let total_tracked_size: usize = self.active_allocations.values().map(|entry| entry.size).sum();

        (active_count, potential_leaks, total_tracked_size)
    }

    /// Enable/disable leak detection
    pub fn set_enabled(&mut self, enabled: bool) {
        self.enabled = enabled;
        if !enabled {
            self.active_allocations.clear();
        }
    }

    /// Clear all tracked allocations
    pub fn clear(&mut self) {
        self.active_allocations.clear();
        self.next_id = 1;
    }
}

/// Memory pool for optimized allocations
#[derive(Debug)]
pub struct MemoryPool {
    /// Pool for small allocations (< 64 bytes)
    small_pool: Vec<u8, 4096>,
    /// Pool for medium allocations (64-512 bytes)
    medium_pool: Vec<u8, 8192>,
    /// Pool for large allocations (> 512 bytes)
    large_pool: Vec<u8, 16384>,
    /// Free block tracking
    free_blocks: Vec<(usize, usize), 256>, // (offset, size) pairs
}

impl MemoryPool {
    /// Create new memory pool
    pub const fn new() -> Self {
        Self {
            small_pool: Vec::new(),
            medium_pool: Vec::new(),
            large_pool: Vec::new(),
            free_blocks: Vec::new(),
        }
    }

    /// Initialize memory pools
    pub fn init(&mut self) -> Result<(), &'static str> {
        // Initialize pools with default capacity
        self.small_pool.resize_default(4096).map_err(|_| "Failed to initialize small pool")?;
        self.medium_pool.resize_default(8192).map_err(|_| "Failed to initialize medium pool")?;
        self.large_pool.resize_default(16384).map_err(|_| "Failed to initialize large pool")?;

        // Initialize free block tracking
        self.free_blocks.push((0, 4096)).map_err(|_| "Failed to initialize free blocks")?;

        Ok(())
    }

    /// Allocate from appropriate pool
    pub fn allocate(&mut self, size: usize, align: usize) -> Option<*mut u8> {
        if size == 0 {
            return None;
        }

        // Choose appropriate pool based on size
        let pool = if size < 64 {
            &mut self.small_pool
        } else if size < 512 {
            &mut self.medium_pool
        } else {
            &mut self.large_pool
        };

        // Find suitable free block
        for (i, &(offset, block_size)) in self.free_blocks.iter().enumerate() {
            if block_size >= size && offset % align == 0 {
                // Remove or split the free block
                if block_size == size {
                    self.free_blocks.swap_remove(i);
                } else {
                    // Split the block
                    self.free_blocks[i] = (offset + size, block_size - size);
                }

                // Return pointer to allocated memory
                return Some(pool.as_mut_ptr().wrapping_add(offset));
            }
        }

        None
    }

    /// Deallocate memory back to pool
    pub fn deallocate(&mut self, ptr: *mut u8, size: usize) {
        // Calculate offset from pool base
        let small_base = self.small_pool.as_ptr() as usize;
        let medium_base = self.medium_pool.as_ptr() as usize;
        let large_base = self.large_pool.as_ptr() as usize;
        let ptr_addr = ptr as usize;

        let offset = if ptr_addr >= small_base && ptr_addr < small_base + self.small_pool.len() {
            ptr_addr - small_base
        } else if ptr_addr >= medium_base && ptr_addr < medium_base + self.medium_pool.len() {
            ptr_addr - medium_base
        } else if ptr_addr >= large_base && ptr_addr < large_base + self.large_pool.len() {
            ptr_addr - large_base
        } else {
            return; // Invalid pointer
        };

        // Add to free blocks (coalescing could be implemented here)
        let _ = self.free_blocks.push((offset, size));
    }

    /// Get fragmentation ratio
    pub fn get_fragmentation_ratio(&self) -> f32 {
        if self.free_blocks.is_empty() {
            return 0.0;
        }

        let total_free: usize = self.free_blocks.iter().map(|(_, size)| *size).sum();
        let largest_free = self.free_blocks.iter().map(|(_, size)| *size).max().unwrap_or(0);

        if total_free == 0 {
            0.0
        } else {
            1.0 - (largest_free as f32 / total_free as f32)
        }
    }

    /// Get largest free block size
    pub fn get_largest_free_block(&self) -> usize {
        self.free_blocks.iter().map(|(_, size)| *size).max().unwrap_or(0)
    }

    /// Phase 12.1 Week 2 - Memory Pool Optimization

    /// Optimize memory pools for better allocation efficiency
    pub fn optimize_pools(&mut self) -> Result<(), &'static str> {
        // Sort free blocks by offset to enable merging
        self.free_blocks.sort_by_key(|&(offset, _)| offset);

        // Merge adjacent free blocks
        self.merge_adjacent_blocks()?;

        // Defragment pools if fragmentation is high
        self.defragment_if_needed()?;

        Ok(())
    }

    /// Merge adjacent free blocks to reduce fragmentation
    fn merge_adjacent_blocks(&mut self) -> Result<(), &'static str> {
        let mut i = 0;
        while i < self.free_blocks.len().saturating_sub(1) {
            let (offset1, size1) = self.free_blocks[i];
            let (offset2, size2) = self.free_blocks[i + 1];

            // Check if blocks are adjacent
            if offset1 + size1 == offset2 {
                // Merge blocks
                self.free_blocks[i] = (offset1, size1 + size2);
                self.free_blocks.swap_remove(i + 1);
                // Don't increment i to check for further merging
            } else {
                i += 1;
            }
        }
        Ok(())
    }

    /// Defragment pools if fragmentation ratio is high
    fn defragment_if_needed(&mut self) -> Result<(), &'static str> {
        let fragmentation_ratio = self.get_fragmentation_ratio();

        if fragmentation_ratio > 0.4 {
            // High fragmentation - attempt defragmentation
            self.compact_free_blocks()?;
        }

        Ok(())
    }

    /// Compact free blocks to reduce fragmentation
    fn compact_free_blocks(&mut self) -> Result<(), &'static str> {
        // Sort free blocks by size (largest first)
        self.free_blocks.sort_by_key(|&(_, size)| core::cmp::Reverse(size));

        // In a real implementation, this would move allocated blocks
        // to create larger contiguous free spaces
        // For now, we just ensure optimal free block organization

        Ok(())
    }

    /// Get pool utilization statistics
    pub fn get_utilization_stats(&self) -> PoolUtilizationStats {
        let total_free_space: usize = self.free_blocks.iter().map(|(_, size)| *size).sum();
        let largest_free_block = self.free_blocks.iter().map(|(_, size)| *size).max().unwrap_or(0);
        let fragmentation_ratio = self.get_fragmentation_ratio();

        let total_pool_size = self.small_pool.capacity() + self.medium_pool.capacity() + self.large_pool.capacity();
        let utilization_percent = if total_pool_size > 0 {
            ((total_pool_size - total_free_space) as f32 / total_pool_size as f32) * 100.0
        } else {
            0.0
        };

        PoolUtilizationStats {
            total_pool_size,
            total_free_space,
            largest_free_block,
            fragmentation_ratio,
            utilization_percent,
            free_block_count: self.free_blocks.len(),
        }
    }
}

/// Pool utilization statistics
#[derive(Debug, Clone)]
pub struct PoolUtilizationStats {
    pub total_pool_size: usize,
    pub total_free_space: usize,
    pub largest_free_block: usize,
    pub fragmentation_ratio: f32,
    pub utilization_percent: f32,
    pub free_block_count: usize,
}

static STATS: Mutex<AllocationStats> = Mutex::new(AllocationStats {
    total_allocated: 0,
    total_freed: 0,
    current_usage: 0,
    peak_usage: 0,
    allocation_count: 0,
    free_count: 0,
    fragmentation_ratio: 0.0,
    largest_free_block: 0,
    allocation_failures: 0,
    total_size: HEAP_SIZE,
    // Phase 12.1 Week 2 additions
    potential_leaks: 0,
    leak_detection_enabled: true,
    memory_efficiency_percent: 100.0,
});

/// Global memory pool for optimized allocations
static MEMORY_POOL: Mutex<Option<MemoryPool>> = Mutex::new(None);

/// Global leak detector for memory leak detection
static LEAK_DETECTOR: Mutex<LeakDetector> = Mutex::new(LeakDetector::new());

/// Initialize the global allocator
pub fn init() {
    unsafe {
        ALLOCATOR.lock().init(HEAP.as_mut_ptr(), HEAP_SIZE);
    }

    // Initialize memory pool
    let mut pool_guard = MEMORY_POOL.lock();
    let mut pool = MemoryPool::new();
    if pool.init().is_ok() {
        *pool_guard = Some(pool);
    }

    // Allocator initialized
}

/// Initialize memory optimization features
pub fn init_optimization() -> Result<(), &'static str> {
    // Update initial statistics
    update_fragmentation_stats()?;

    // Initialize memory pools for common allocation sizes
    init_memory_pools()?;

    // Enable cache-aligned allocations for performance
    enable_cache_alignment()?;

    Ok(())
}

/// Initialize memory pools for common allocation patterns
fn init_memory_pools() -> Result<(), &'static str> {
    let mut pool_guard = MEMORY_POOL.lock();
    if let Some(ref mut pool) = *pool_guard {
        // Pre-allocate blocks for common sizes
        pool.preallocate_blocks(SMALL_POOL_SIZE, POOL_BLOCK_COUNT)?;
        pool.preallocate_blocks(MEDIUM_POOL_SIZE, POOL_BLOCK_COUNT)?;
        pool.preallocate_blocks(LARGE_POOL_SIZE, POOL_BLOCK_COUNT / 2)?;
    }
    Ok(())
}

/// Enable cache-aligned allocations for better performance
fn enable_cache_alignment() -> Result<(), &'static str> {
    // This would configure the allocator to align allocations to cache line boundaries
    // Implementation depends on the specific allocator being used
    Ok(())
}

/// Update fragmentation statistics
pub fn update_fragmentation_stats() -> Result<(), &'static str> {
    let mut stats = STATS.lock();

    if let Some(pool) = MEMORY_POOL.lock().as_ref() {
        stats.fragmentation_ratio = pool.get_fragmentation_ratio();
        stats.largest_free_block = pool.get_largest_free_block();
    }

    Ok(())
}

/// Perform memory optimization
pub fn optimize_memory() -> Result<(), &'static str> {
    // Update statistics first
    update_fragmentation_stats()?;

    let stats = get_stats();

    // If fragmentation is high, we could trigger compaction
    if stats.fragmentation_ratio > 0.5 {
        // In a real implementation, this would trigger memory compaction
        // For now, just update statistics
        return Ok(());
    }

    Ok(())
}

/// Get memory usage percentage
pub fn get_memory_usage_percent() -> u8 {
    let stats = get_stats();
    if HEAP_SIZE == 0 {
        return 0;
    }

    ((stats.current_usage * 100) / HEAP_SIZE).min(100) as u8
}

/// Check if memory usage is critical
pub fn is_memory_critical() -> bool {
    get_memory_usage_percent() > 90
}

/// Get available memory
pub fn get_available_memory() -> usize {
    let stats = get_stats();
    HEAP_SIZE.saturating_sub(stats.current_usage)
}

/// Allocate with optimization hints
pub fn allocate_optimized(size: usize, align: usize) -> Option<*mut u8> {
    // Try optimized allocation from memory pool first
    if let Some(pool) = MEMORY_POOL.lock().as_mut() {
        if let Some(ptr) = pool.allocate(size, align) {
            // Update statistics
            let mut stats = STATS.lock();
            stats.total_allocated += size;
            stats.current_usage += size;
            stats.allocation_count += 1;

            if stats.current_usage > stats.peak_usage {
                stats.peak_usage = stats.current_usage;
            }

            return Some(ptr);
        }
    }

    // Fall back to standard allocation
    let layout = Layout::from_size_align(size, align).ok()?;
    let ptr = unsafe { ALLOCATOR.alloc(layout) };

    if !ptr.is_null() {
        let mut stats = STATS.lock();
        stats.total_allocated += size;
        stats.current_usage += size;
        stats.allocation_count += 1;

        if stats.current_usage > stats.peak_usage {
            stats.peak_usage = stats.current_usage;
        }

        Some(ptr)
    } else {
        // Track allocation failure
        STATS.lock().allocation_failures += 1;
        None
    }
}

/// Deallocate with optimization
pub fn deallocate_optimized(ptr: *mut u8, size: usize) {
    // Try to deallocate from memory pool first
    if let Some(pool) = MEMORY_POOL.lock().as_mut() {
        pool.deallocate(ptr, size);

        // Update statistics
        let mut stats = STATS.lock();
        stats.total_freed += size;
        stats.current_usage = stats.current_usage.saturating_sub(size);
        stats.free_count += 1;

        return;
    }

    // Fall back to standard deallocation
    let layout = Layout::from_size_align(size, 1).unwrap();
    unsafe {
        ALLOCATOR.dealloc(ptr, layout);
    }

    let mut stats = STATS.lock();
    stats.total_freed += size;
    stats.current_usage = stats.current_usage.saturating_sub(size);
    stats.free_count += 1;
}

/// Get current allocation statistics
pub fn get_stats() -> AllocationStats {
    *STATS.lock()
}

/// Custom allocator wrapper for statistics tracking
pub struct TrackingAllocator;

unsafe impl GlobalAlloc for TrackingAllocator {
    unsafe fn alloc(&self, layout: Layout) -> *mut u8 {
        let ptr = ALLOCATOR.alloc(layout);
        
        if !ptr.is_null() {
            let mut stats = STATS.lock();
            stats.total_allocated += layout.size();
            stats.current_usage += layout.size();
            stats.allocation_count += 1;
            
            if stats.current_usage > stats.peak_usage {
                stats.peak_usage = stats.current_usage;
            }
        }
        
        ptr
    }
    
    unsafe fn dealloc(&self, ptr: *mut u8, layout: Layout) {
        ALLOCATOR.dealloc(ptr, layout);
        
        let mut stats = STATS.lock();
        stats.total_freed += layout.size();
        stats.current_usage = stats.current_usage.saturating_sub(layout.size());
        stats.free_count += 1;
    }
}

/// Emergency allocation for critical system operations
/// This uses a separate, reserved memory pool
static mut EMERGENCY_HEAP: [u8; EMERGENCY_HEAP_SIZE] = [0; EMERGENCY_HEAP_SIZE];
const EMERGENCY_HEAP_SIZE: usize = 4 * 1024; // 4KB emergency heap
static EMERGENCY_ALLOCATOR: LockedHeap = LockedHeap::empty();
static EMERGENCY_INITIALIZED: Mutex<bool> = Mutex::new(false);

/// Initialize emergency allocator
pub fn init_emergency() {
    let mut initialized = EMERGENCY_INITIALIZED.lock();
    if !*initialized {
        unsafe {
            EMERGENCY_ALLOCATOR.lock().init(
                EMERGENCY_HEAP.as_mut_ptr(), 
                EMERGENCY_HEAP_SIZE
            );
        }
        *initialized = true;
    }
}

/// Allocate from emergency heap (for critical operations only)
pub unsafe fn emergency_alloc(layout: Layout) -> *mut u8 {
    init_emergency();
    EMERGENCY_ALLOCATOR.alloc(layout)
}

/// Free emergency allocation
pub unsafe fn emergency_dealloc(ptr: *mut u8, layout: Layout) {
    EMERGENCY_ALLOCATOR.dealloc(ptr, layout);
}

/// Perform garbage collection if supported
pub fn gc_if_needed() {
    if is_memory_critical() {
        // In a more advanced implementation, this could trigger
        // garbage collection or memory compaction
        // Warning: Memory usage critical
    }
}

/// Enable advanced memory optimization
pub fn enable_advanced_optimization() -> Result<(), &'static str> {
    init_optimization()?;
    optimize_memory()?;
    Ok(())
}

/// Phase 12.1 Week 2 - Memory Usage Optimization Functions

/// Enable memory leak detection
pub fn enable_leak_detection() -> Result<(), &'static str> {
    let mut detector = LEAK_DETECTOR.lock();
    detector.set_enabled(true);

    let mut stats = STATS.lock();
    stats.leak_detection_enabled = true;

    Ok(())
}

/// Disable memory leak detection
pub fn disable_leak_detection() -> Result<(), &'static str> {
    let mut detector = LEAK_DETECTOR.lock();
    detector.set_enabled(false);

    let mut stats = STATS.lock();
    stats.leak_detection_enabled = false;

    Ok(())
}

/// Perform memory leak detection scan
pub fn detect_memory_leaks() -> usize {
    let detector = LEAK_DETECTOR.lock();
    let leak_count = detector.detect_leaks();

    // Update statistics
    let mut stats = STATS.lock();
    stats.potential_leaks = leak_count;

    leak_count
}

/// Get detailed leak detection statistics
pub fn get_leak_detection_stats() -> (usize, usize, usize) {
    LEAK_DETECTOR.lock().get_stats()
}

/// Optimize memory allocation patterns
pub fn optimize_allocation_patterns() -> Result<(), &'static str> {
    // Analyze current allocation patterns
    let stats = get_stats();

    // Calculate memory efficiency
    let efficiency = if stats.total_allocated > 0 {
        ((stats.total_allocated - stats.current_usage) as f32 / stats.total_allocated as f32) * 100.0
    } else {
        100.0
    };

    // Update efficiency in statistics
    {
        let mut stats_guard = STATS.lock();
        stats_guard.memory_efficiency_percent = efficiency;
    }

    // Trigger garbage collection if efficiency is low
    if efficiency < 70.0 {
        gc_if_needed();
    }

    Ok(())
}

/// Reduce memory fragmentation
pub fn reduce_fragmentation() -> Result<(), &'static str> {
    // In a real implementation, this would:
    // 1. Analyze free block distribution
    // 2. Compact memory if possible
    // 3. Merge adjacent free blocks
    // 4. Reorganize memory pools

    if let Some(pool) = MEMORY_POOL.lock().as_mut() {
        // Optimize memory pools
        pool.optimize_pools()?;
    }

    Ok(())
}

/// Allocate with leak detection tracking
pub fn allocate_tracked(size: usize, align: usize, call_site: &'static str) -> Option<*mut u8> {
    let ptr = allocate_optimized(size, align)?;

    // Track allocation for leak detection
    if !ptr.is_null() {
        LEAK_DETECTOR.lock().track_allocation(ptr, size, call_site);
    }

    Some(ptr)
}

/// Deallocate with leak detection tracking
pub fn deallocate_tracked(ptr: *mut u8, size: usize) {
    // Track deallocation for leak detection
    LEAK_DETECTOR.lock().track_deallocation(ptr);

    deallocate_optimized(ptr, size);
}

/// Get memory optimization recommendations
pub fn get_optimization_recommendations() -> Vec<&'static str, 8> {
    let mut recommendations = Vec::new();
    let stats = get_stats();
    let (active_allocs, potential_leaks, _) = get_leak_detection_stats();

    // Check for potential leaks
    if potential_leaks > 0 {
        let _ = recommendations.push("Memory leaks detected - review allocation patterns");
    }

    // Check memory efficiency
    if stats.memory_efficiency_percent < 80.0 {
        let _ = recommendations.push("Low memory efficiency - consider memory pool optimization");
    }

    // Check fragmentation
    if stats.fragmentation_ratio > 0.3 {
        let _ = recommendations.push("High fragmentation detected - run defragmentation");
    }

    // Check allocation failures
    if stats.allocation_failures > 0 {
        let _ = recommendations.push("Allocation failures detected - increase heap size or optimize usage");
    }

    // Check peak usage
    let usage_percent = (stats.peak_usage * 100) / stats.total_size;
    if usage_percent > 90 {
        let _ = recommendations.push("High peak memory usage - monitor for memory pressure");
    }

    // Check active allocations
    if active_allocs > 400 {
        let _ = recommendations.push("High number of active allocations - consider memory pooling");
    }

    recommendations
}

/// Perform comprehensive memory optimization
pub fn perform_memory_optimization() -> Result<(), &'static str> {
    // Step 1: Detect memory leaks
    let leak_count = detect_memory_leaks();

    // Step 2: Optimize allocation patterns
    optimize_allocation_patterns()?;

    // Step 3: Reduce fragmentation
    reduce_fragmentation()?;

    // Step 4: Garbage collection if needed
    if leak_count > 0 || is_memory_critical() {
        gc_if_needed();
    }

    Ok(())
}

/// Get pool utilization statistics
pub fn get_pool_utilization() -> Option<PoolUtilizationStats> {
    MEMORY_POOL.lock().as_ref().map(|pool| pool.get_utilization_stats())
}

/// Update allocator statistics with current state
pub fn update_allocator_statistics() -> Result<(), &'static str> {
    let mut stats = STATS.lock();

    // Update leak detection statistics
    let (active_allocs, potential_leaks, _) = get_leak_detection_stats();
    stats.potential_leaks = potential_leaks;

    // Update fragmentation ratio from memory pool
    if let Some(pool_stats) = get_pool_utilization() {
        stats.fragmentation_ratio = pool_stats.fragmentation_ratio;
        stats.largest_free_block = pool_stats.largest_free_block;
    }

    // Update memory efficiency
    if stats.total_allocated > 0 {
        stats.memory_efficiency_percent =
            ((stats.total_allocated - stats.current_usage) as f32 / stats.total_allocated as f32) * 100.0;
    }

    Ok(())
}

/// Initialize memory optimization system
pub fn init_memory_optimization() -> Result<(), &'static str> {
    // Enable leak detection by default
    enable_leak_detection()?;

    // Initialize memory pools if not already done
    let mut pool_guard = MEMORY_POOL.lock();
    if pool_guard.is_none() {
        let mut pool = MemoryPool::new();
        pool.init()?;
        *pool_guard = Some(pool);
    }

    // Update initial statistics
    drop(pool_guard);
    update_allocator_statistics()?;

    Ok(())
}

/// Memory optimization report
#[derive(Debug, Clone)]
pub struct MemoryOptimizationReport {
    pub total_memory: usize,
    pub used_memory: usize,
    pub free_memory: usize,
    pub peak_usage: usize,
    pub allocation_count: usize,
    pub deallocation_count: usize,
    pub potential_leaks: usize,
    pub fragmentation_ratio: f32,
    pub memory_efficiency_percent: f32,
    pub pool_utilization: Option<PoolUtilizationStats>,
    pub recommendations: Vec<&'static str, 8>,
}

/// Generate comprehensive memory optimization report
pub fn generate_memory_report() -> MemoryOptimizationReport {
    let stats = get_stats();
    let pool_utilization = get_pool_utilization();
    let recommendations = get_optimization_recommendations();

    MemoryOptimizationReport {
        total_memory: stats.total_size,
        used_memory: stats.current_usage,
        free_memory: stats.total_size.saturating_sub(stats.current_usage),
        peak_usage: stats.peak_usage,
        allocation_count: stats.allocation_count,
        deallocation_count: stats.free_count,
        potential_leaks: stats.potential_leaks,
        fragmentation_ratio: stats.fragmentation_ratio,
        memory_efficiency_percent: stats.memory_efficiency_percent,
        pool_utilization,
        recommendations,
    }
}
