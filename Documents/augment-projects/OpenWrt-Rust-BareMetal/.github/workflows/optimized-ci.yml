name: Optimized OpenWrt Rust Firmware CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run nightly builds to catch regressions
    - cron: '0 2 * * *'

env:
  CARGO_TERM_COLOR: always
  RUST_BACKTRACE: 1

jobs:
  # Fast preliminary checks
  quick-checks:
    name: Quick Checks
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Install Rust toolchain
      uses: dtolnay/rust-toolchain@stable
      with:
        components: rustfmt, clippy
    
    - name: Cache cargo registry
      uses: actions/cache@v3
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          target
        key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
    
    - name: Check formatting
      run: cargo fmt --all -- --check
    
    - name: Run clippy
      run: cargo clippy --all-targets --all-features -- -D warnings

  # Build matrix for different targets and optimization levels
  build-matrix:
    name: Build (${{ matrix.target }}, ${{ matrix.profile }})
    runs-on: ubuntu-latest
    needs: quick-checks
    strategy:
      fail-fast: false
      matrix:
        target:
          - x86_64-unknown-none
          - aarch64-unknown-none
          - riscv64gc-unknown-none
        profile:
          - release
          - embedded
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Install Rust toolchain
      uses: dtolnay/rust-toolchain@nightly
      with:
        targets: ${{ matrix.target }}
        components: rust-src
    
    - name: Cache cargo
      uses: actions/cache@v3
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          target
        key: ${{ runner.os }}-${{ matrix.target }}-${{ matrix.profile }}-${{ hashFiles('**/Cargo.lock') }}
    
    - name: Install build dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y build-essential
    
    - name: Build firmware
      run: |
        cargo build \
          --target ${{ matrix.target }} \
          --profile ${{ matrix.profile }} \
          --features bare-metal \
          --no-default-features \
          -Z build-std=core,alloc \
          -Z build-std-features=panic_immediate_abort
    
    - name: Check binary size
      run: |
        ls -la target/${{ matrix.target }}/${{ matrix.profile }}/
        find target/${{ matrix.target }}/${{ matrix.profile }}/ -type f -executable -exec ls -lh {} \;
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: firmware-${{ matrix.target }}-${{ matrix.profile }}
        path: target/${{ matrix.target }}/${{ matrix.profile }}/
        retention-days: 7

  # Performance benchmarks
  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: quick-checks
    steps:
    - uses: actions/checkout@v4
    
    - name: Install Rust toolchain
      uses: dtolnay/rust-toolchain@stable
    
    - name: Cache cargo
      uses: actions/cache@v3
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          target
        key: ${{ runner.os }}-perf-${{ hashFiles('**/Cargo.lock') }}
    
    - name: Run performance benchmarks
      run: cargo test --test performance_benchmarks --release
    
    - name: Generate performance report
      run: |
        echo "## Performance Test Results" > performance-report.md
        echo "Generated on: $(date)" >> performance-report.md
        echo "" >> performance-report.md
        cargo test --test performance_benchmarks --release -- --nocapture >> performance-report.md 2>&1 || true
    
    - name: Upload performance report
      uses: actions/upload-artifact@v3
      with:
        name: performance-report
        path: performance-report.md

  # Security and safety checks
  security-audit:
    name: Security Audit
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Install Rust toolchain
      uses: dtolnay/rust-toolchain@stable
    
    - name: Install cargo-audit
      run: cargo install cargo-audit
    
    - name: Run security audit
      run: cargo audit
    
    - name: Check for unsafe code
      run: |
        echo "Checking for unsafe code blocks..."
        grep -r "unsafe" src/ || echo "No unsafe code found"
        
        # Count unsafe blocks
        UNSAFE_COUNT=$(grep -r "unsafe" src/ | wc -l || echo "0")
        echo "Total unsafe code instances: $UNSAFE_COUNT"
        
        # Fail if too many unsafe blocks (threshold: 50)
        if [ "$UNSAFE_COUNT" -gt 50 ]; then
          echo "Too many unsafe code blocks: $UNSAFE_COUNT (max: 50)"
          exit 1
        fi

  # Memory safety validation
  memory-safety:
    name: Memory Safety
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Install Rust toolchain
      uses: dtolnay/rust-toolchain@nightly
      with:
        components: miri
    
    - name: Run Miri for memory safety
      run: |
        # Run Miri on critical components
        cargo miri test --lib memory
        cargo miri test --lib allocator
      continue-on-error: true  # Miri might not work with all no_std code

  # Size optimization validation
  size-optimization:
    name: Size Optimization
    runs-on: ubuntu-latest
    needs: build-matrix
    steps:
    - uses: actions/checkout@v4
    
    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: firmware-x86_64-unknown-none-embedded
        path: ./artifacts/
    
    - name: Install size analysis tools
      run: |
        cargo install cargo-bloat || true
        sudo apt-get install -y binutils
    
    - name: Analyze binary size
      run: |
        echo "## Binary Size Analysis" > size-report.md
        echo "Generated on: $(date)" >> size-report.md
        echo "" >> size-report.md
        
        # List all binaries and their sizes
        echo "### Binary Sizes" >> size-report.md
        find ./artifacts/ -type f -executable -exec ls -lh {} \; >> size-report.md
        
        # Calculate total size
        TOTAL_SIZE=$(find ./artifacts/ -type f -executable -exec stat -c%s {} \; | awk '{sum+=$1} END {print sum}')
        echo "" >> size-report.md
        echo "**Total firmware size: $(numfmt --to=iec-i --suffix=B $TOTAL_SIZE)**" >> size-report.md
        
        # Size constraint check (2MB limit)
        if [ "$TOTAL_SIZE" -gt 2097152 ]; then
          echo "❌ Firmware size exceeds 2MB limit!" >> size-report.md
          exit 1
        else
          echo "✅ Firmware size within 2MB limit" >> size-report.md
        fi
    
    - name: Upload size report
      uses: actions/upload-artifact@v3
      with:
        name: size-report
        path: size-report.md

  # Integration test summary
  integration-summary:
    name: Integration Summary
    runs-on: ubuntu-latest
    needs: [build-matrix, performance-tests, security-audit, size-optimization]
    if: always()
    steps:
    - name: Download all artifacts
      uses: actions/download-artifact@v3
    
    - name: Generate integration summary
      run: |
        echo "# OpenWrt Rust Firmware CI Summary" > integration-summary.md
        echo "Generated on: $(date)" >> integration-summary.md
        echo "" >> integration-summary.md
        
        echo "## Build Status" >> integration-summary.md
        echo "- Build Matrix: ${{ needs.build-matrix.result }}" >> integration-summary.md
        echo "- Performance Tests: ${{ needs.performance-tests.result }}" >> integration-summary.md
        echo "- Security Audit: ${{ needs.security-audit.result }}" >> integration-summary.md
        echo "- Size Optimization: ${{ needs.size-optimization.result }}" >> integration-summary.md
        echo "" >> integration-summary.md
        
        echo "## Artifacts Generated" >> integration-summary.md
        ls -la >> integration-summary.md
        
        # Overall status
        if [[ "${{ needs.build-matrix.result }}" == "success" && 
              "${{ needs.performance-tests.result }}" == "success" && 
              "${{ needs.security-audit.result }}" == "success" && 
              "${{ needs.size-optimization.result }}" == "success" ]]; then
          echo "" >> integration-summary.md
          echo "🎉 **All checks passed! Firmware ready for deployment.**" >> integration-summary.md
        else
          echo "" >> integration-summary.md
          echo "❌ **Some checks failed. Review before deployment.**" >> integration-summary.md
        fi
    
    - name: Upload integration summary
      uses: actions/upload-artifact@v3
      with:
        name: integration-summary
        path: integration-summary.md
